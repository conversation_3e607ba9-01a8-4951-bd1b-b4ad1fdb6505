import json
import os

CONFIG_PATH = os.path.join('backend/src/servers/worker', 'config.json')
with open(CONFIG_PATH, 'r') as f:
    CONFIG = json.load(f)

def get_platform_config(platform: str):
    defaults = CONFIG.get('defaults', {})
    platform_config = CONFIG.get('platforms', {}).get(platform, {})
    config = defaults.copy()
    config.update(platform_config)
    return config

# Test shopify config
shopify_config = get_platform_config('shopify')
print('Shopify entity_types:', shopify_config['entity_types'])
print('Shopify check_interval:', shopify_config['sync_check_interval_minutes'])
print('Shopify cleanup_days:', shopify_config['sync_cleanup_days_old'])

# Test non-existent platform (should use defaults)
other_config = get_platform_config('other')
print('Other entity_types:', other_config['entity_types'])
print('Other check_interval:', other_config['sync_check_interval_minutes'])
print('Other cleanup_days:', other_config['sync_cleanup_days_old'])