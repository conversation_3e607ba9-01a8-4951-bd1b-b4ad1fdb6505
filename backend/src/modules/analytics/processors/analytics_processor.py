import logging
from typing import Dict, Any, List
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from core.db.database import get_db_session_factory
from modules.analytics.event_service import analytics_event_service
from modules.analytics.event_schemas import EventIngestionRequest
from core.metrics import analytics_processing_duration, analytics_processing_failures

logger = logging.getLogger(__name__)


class AnalyticsProcessor:
    """Enhanced analytics processor with batch processing and retry logic."""

    def __init__(self):
        self.db_factory = get_db_session_factory()
        self.max_batch_size = 50  # Process events in batches
        self.max_retries = 3

    @asynccontextmanager
    async def get_db_session(self):
        """Context manager for database sessions."""
        db = self.db_factory()
        try:
            yield db
        finally:
            await db.close()

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analytics events with enhanced error handling.

        Args:
            job_data: Job data dictionary containing events

        Returns:
            Job result with processing statistics
        """
        import asyncio
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation with batch processing and comprehensive error handling.
        """
        tenant_id = job_data.get("tenant_id")
        if not tenant_id:
            raise ValueError("tenant_id is required")

        start_time = datetime.now(timezone.utc)

        # Handle both single event and batch events
        events_data = job_data.get("events", [job_data.get("event_data")])
        if not events_data or events_data[0] is None:
            raise ValueError("No events data provided")

        logger.info(f"Processing {len(events_data)} analytics events for tenant {tenant_id}")

        # Process events in batches
        successful_events = 0
        failed_events = 0
        processed_event_ids = []

        try:
            async with self.get_db_session() as db:
                for i in range(0, len(events_data), self.max_batch_size):
                    batch = events_data[i:i + self.max_batch_size]
                    batch_successful, batch_failed, batch_ids = await self._process_batch(
                        db, tenant_id, batch
                    )
                    successful_events += batch_successful
                    failed_events += batch_failed
                    processed_event_ids.extend(batch_ids)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Record metrics
            analytics_processing_duration.observe(duration)

            result = {
                "success": failed_events == 0,
                "events_processed": successful_events,
                "events_failed": failed_events,
                "total_events": len(events_data),
                "duration_seconds": duration,
                "event_ids": processed_event_ids
            }

            if successful_events > 0:
                logger.info(f"Analytics processing completed: {successful_events} successful, {failed_events} failed")
            else:
                logger.error(f"Analytics processing failed: all {failed_events} events failed")

            return result

        except Exception as e:
            logger.error(f"Critical error in analytics processing: {e}")

            # Record failure metrics
            analytics_processing_failures.labels(
                failure_reason="critical_error"
            ).inc()

            raise e

    async def _process_batch(
        self,
        db,
        tenant_id: int,
        events_batch: List[Dict[str, Any]]
    ) -> tuple[int, int, List[str]]:
        """
        Process a batch of analytics events.

        Returns:
            Tuple of (successful_count, failed_count, event_ids)
        """
        successful = 0
        failed = 0
        event_ids = []

        for event_data in events_batch:
            try:
                # Validate event data
                if not isinstance(event_data, dict):
                    raise ValueError("Event data must be a dictionary")

                # Create event request
                event_request = EventIngestionRequest(**event_data)

                # Process with retry logic
                response = await self._process_single_event_with_retry(
                    db, tenant_id, event_request
                )

                successful += 1
                if hasattr(response, 'event_id'):
                    event_ids.append(response.event_id)

                logger.debug(f"Successfully processed event: {response.status}")

            except Exception as e:
                failed += 1
                logger.error(f"Failed to process event: {e}")

                # Record failure metrics
                analytics_processing_failures.labels(
                    failure_reason="event_processing_error"
                ).inc()

        return successful, failed, event_ids

    async def _process_single_event_with_retry(
        self,
        db,
        tenant_id: int,
        event_request: EventIngestionRequest
    ):
        """Process a single event with retry logic."""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                response = await analytics_event_service.ingest_event(
                    db, tenant_id, event_request
                )
                return response

            except Exception as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    # Exponential backoff
                    import asyncio
                    await asyncio.sleep(2 ** attempt)
                    logger.warning(f"Event processing attempt {attempt + 1} failed: {e}")
                else:
                    logger.error(f"Event processing failed after {self.max_retries} attempts: {e}")

        raise last_exception