"""
Billing API router for ProductVideo platform.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Header
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from .service import billing_service
from .stripe_service import stripe_service
from .schemas import (
    Tenant, TenantCreate, TenantUpdate,
    Subscription, CreateSubscriptionRequest, CreateSubscriptionResponse,
    BillingUsage, RecordUsageRequest, RecordUsageResponse,
    UsageSummary, BillingDashboardData,
    Invoice, PaymentMethod
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/tenants", response_model=Tenant)
async def create_tenant(
    tenant_data: TenantCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new tenant."""
    try:
        tenant = await billing_service.create_tenant(db, tenant_data)
        return tenant
    except Exception as e:
        logger.error(f"Error creating tenant: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}", response_model=Tenant)
async def get_tenant(
    tenant_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get tenant by ID."""
    tenant = await billing_service.get(db, tenant_id)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant


@router.put("/tenants/{tenant_id}", response_model=Tenant)
async def update_tenant(
    tenant_id: int,
    tenant_data: TenantUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update tenant."""
    tenant = await billing_service.update(db, tenant_id, tenant_data)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant


@router.get("/tenants", response_model=List[Tenant])
async def list_tenants(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """List tenants."""
    tenants = await billing_service.get_multi(db, skip=skip, limit=limit)
    return tenants


@router.post("/subscriptions", response_model=CreateSubscriptionResponse)
async def create_subscription(
    request: CreateSubscriptionRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a subscription for a tenant."""
    try:
        response = await stripe_service.create_subscription(db, request)
        return response
    except Exception as e:
        logger.error(f"Error creating subscription: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/usage", response_model=RecordUsageResponse)
async def record_usage(
    request: RecordUsageRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record usage for a tenant."""
    try:
        response = await stripe_service.record_usage(db, request)
        return response
    except Exception as e:
        logger.error(f"Error recording usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/usage", response_model=UsageSummary)
async def get_usage_summary(
    tenant_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get usage summary for a tenant."""
    try:
        summary = await billing_service.get_usage_summary(db, tenant_id)
        return summary
    except Exception as e:
        logger.error(f"Error getting usage summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/dashboard", response_model=BillingDashboardData)
async def get_billing_dashboard(
    tenant_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get billing dashboard data for a tenant."""
    try:
        dashboard_data = await billing_service.get_billing_dashboard_data(db, tenant_id)
        return dashboard_data
    except Exception as e:
        logger.error(f"Error getting billing dashboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/stripe")
async def stripe_webhook(
    request: Request,
    stripe_signature: str = Header(..., alias="stripe-signature"),
    db: AsyncSession = Depends(get_db),
):
    """Handle Stripe webhooks."""
    try:
        # Get raw body
        body = await request.body()
        
        # Process webhook
        result = await stripe_service.process_webhook(db, body, stripe_signature)
        
        return {"status": "success", "result": result}
        
    except ValueError as e:
        logger.error(f"Invalid Stripe webhook: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error processing Stripe webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/usage/check")
async def check_usage_limits(
    tenant_id: int,
    usage_type: str,
    quantity: float,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Check if tenant can use the requested quantity."""
    try:
        from .models import UsageType
        
        # Convert string to enum
        try:
            usage_type_enum = UsageType(usage_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid usage type: {usage_type}")
        
        result = await billing_service.check_usage_limits(
            db, tenant_id, usage_type_enum, quantity
        )
        return result
        
    except Exception as e:
        logger.error(f"Error checking usage limits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/usage/video-generation")
async def record_video_generation_usage(
    tenant_id: int,
    video_job_id: str,
    variant_count: int = 4,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record video generation usage for a tenant."""
    try:
        usage_record = await billing_service.record_video_generation_usage(
            db, tenant_id, video_job_id, variant_count
        )
        return {"status": "success", "usage_record_id": usage_record.id}
        
    except Exception as e:
        logger.error(f"Error recording video generation usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/usage/storage")
async def record_storage_usage(
    tenant_id: int,
    storage_gb: float,
    resource_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record storage usage for a tenant."""
    try:
        usage_record = await billing_service.record_storage_usage(
            db, tenant_id, storage_gb, resource_id
        )
        return {"status": "success", "usage_record_id": usage_record.id}
        
    except Exception as e:
        logger.error(f"Error recording storage usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


