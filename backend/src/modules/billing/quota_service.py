"""
Quota and Cost Control Service
Manages per-merchant quotas, generation budgets, and cost tracking.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class QuotaType(str, Enum):
    """Types of quotas."""
    DAILY = "daily"
    MONTHLY = "monthly"
    TOTAL = "total"


class MediaType(str, Enum):
    """Media types for quota tracking."""
    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"
    VOICE = "voice"


class QuotaService:
    """Service for managing quotas and cost controls."""
    
    def __init__(self):
        self.default_quotas = self._get_default_quotas()
        self.cost_per_generation = self._get_cost_structure()
    
    def _get_default_quotas(self) -> Dict[str, Dict[str, int]]:
        """Get default quotas by plan tier."""
        return {
            "free": {
                "daily_images": 10,
                "daily_videos": 2,
                "daily_text": 50,
                "monthly_images": 100,
                "monthly_videos": 20,
                "monthly_text": 500,
                "total_storage_mb": 100
            },
            "starter": {
                "daily_images": 50,
                "daily_videos": 10,
                "daily_text": 200,
                "monthly_images": 1000,
                "monthly_videos": 100,
                "monthly_text": 5000,
                "total_storage_mb": 1000
            },
            "growth": {
                "daily_images": 200,
                "daily_videos": 50,
                "daily_text": 1000,
                "monthly_images": 5000,
                "monthly_videos": 500,
                "monthly_text": 20000,
                "total_storage_mb": 10000
            },
            "pro": {
                "daily_images": 1000,
                "daily_videos": 200,
                "daily_text": 5000,
                "monthly_images": 25000,
                "monthly_videos": 2000,
                "monthly_text": 100000,
                "total_storage_mb": 50000
            },
            "enterprise": {
                "daily_images": -1,  # Unlimited
                "daily_videos": -1,
                "daily_text": -1,
                "monthly_images": -1,
                "monthly_videos": -1,
                "monthly_text": -1,
                "total_storage_mb": -1
            }
        }
    
    def _get_cost_structure(self) -> Dict[str, float]:
        """Get cost per generation by media type."""
        return {
            "image": 0.02,  # $0.02 per image
            "video": 0.50,  # $0.50 per video
            "text": 0.001,  # $0.001 per text generation
            "voice": 0.05   # $0.05 per voice generation
        }
    
    async def check_quota(
        self, 
        user_id: int, 
        media_type: str, 
        quantity: int = 1
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if user has quota available for generation.
        
        Returns:
            Tuple of (has_quota, quota_info)
        """
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select, and_, func
            from modules.auth.models import User
            from modules.media.models import MediaJob
            
            db = SessionLocal()
            
            # Get user and plan
            user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
            if not user:
                return False, {"error": "User not found"}
            
            plan_tier = getattr(user, 'plan_tier', 'free')
            quotas = self.default_quotas.get(plan_tier, self.default_quotas['free'])
            
            # Check daily quota
            today = datetime.now().date()
            daily_key = f"daily_{media_type}s"
            daily_limit = quotas.get(daily_key, 0)
            
            if daily_limit > 0:  # -1 means unlimited
                daily_usage = db.execute(
                    select(func.count(MediaJob.id)).filter(
                        and_(
                            MediaJob.user_id == user_id,
                            MediaJob.media_type == media_type,
                            func.date(MediaJob.created_at) == today
                        )
                    )
                ).scalar() or 0
                
                if daily_usage + quantity > daily_limit:
                    db.close()
                    return False, {
                        "quota_type": "daily",
                        "limit": daily_limit,
                        "used": daily_usage,
                        "requested": quantity,
                        "available": max(0, daily_limit - daily_usage)
                    }
            
            # Check monthly quota
            month_start = datetime.now().replace(day=1).date()
            monthly_key = f"monthly_{media_type}s"
            monthly_limit = quotas.get(monthly_key, 0)
            
            if monthly_limit > 0:  # -1 means unlimited
                monthly_usage = db.execute(
                    select(func.count(MediaJob.id)).filter(
                        and_(
                            MediaJob.user_id == user_id,
                            MediaJob.media_type == media_type,
                            func.date(MediaJob.created_at) >= month_start
                        )
                    )
                ).scalar() or 0
                
                if monthly_usage + quantity > monthly_limit:
                    db.close()
                    return False, {
                        "quota_type": "monthly",
                        "limit": monthly_limit,
                        "used": monthly_usage,
                        "requested": quantity,
                        "available": max(0, monthly_limit - monthly_usage)
                    }
            
            db.close()
            
            return True, {
                "quota_type": "available",
                "daily_limit": daily_limit,
                "monthly_limit": monthly_limit,
                "plan_tier": plan_tier
            }
            
        except Exception as e:
            logger.error(f"Quota check failed: {e}")
            return False, {"error": str(e)}
    
    async def check_budget(
        self, 
        user_id: int, 
        media_type: str, 
        quantity: int = 1
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if user has budget available for generation.
        
        Returns:
            Tuple of (has_budget, budget_info)
        """
        try:
            cost_per_item = self.cost_per_generation.get(media_type, 0.02)
            total_cost = cost_per_item * quantity
            
            # Get user's current budget/credits
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.auth.models import User
            
            db = SessionLocal()
            user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
            
            if not user:
                db.close()
                return False, {"error": "User not found"}
            
            # Check if user has credits/budget (assuming a credits field exists)
            user_credits = getattr(user, 'credits', 0.0)
            
            if user_credits < total_cost:
                db.close()
                return False, {
                    "budget_type": "insufficient_credits",
                    "required": total_cost,
                    "available": user_credits,
                    "deficit": total_cost - user_credits
                }
            
            db.close()
            
            return True, {
                "budget_type": "sufficient",
                "cost": total_cost,
                "available_credits": user_credits,
                "remaining_after": user_credits - total_cost
            }
            
        except Exception as e:
            logger.error(f"Budget check failed: {e}")
            return False, {"error": str(e)}
    
    async def deduct_quota_and_budget(
        self, 
        user_id: int, 
        media_type: str, 
        quantity: int = 1
    ) -> bool:
        """
        Deduct quota and budget after successful generation.
        
        Returns:
            True if deduction successful
        """
        try:
            cost_per_item = self.cost_per_generation.get(media_type, 0.02)
            total_cost = cost_per_item * quantity
            
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.auth.models import User
            
            db = SessionLocal()
            user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
            
            if not user:
                db.close()
                return False
            
            # Deduct credits if applicable
            if hasattr(user, 'credits'):
                user.credits = max(0, user.credits - total_cost)
                db.commit()
            
            db.close()
            
            logger.info(f"Deducted {total_cost} credits for {quantity} {media_type} generations for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Quota/budget deduction failed: {e}")
            return False
    
    async def get_usage_stats(self, user_id: int) -> Dict[str, Any]:
        """Get usage statistics for a user."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select, and_, func
            from modules.auth.models import User
            from modules.media.models import MediaJob
            
            db = SessionLocal()
            
            # Get user and plan
            user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
            if not user:
                return {"error": "User not found"}
            
            plan_tier = getattr(user, 'plan_tier', 'free')
            quotas = self.default_quotas.get(plan_tier, self.default_quotas['free'])
            
            # Calculate usage for different periods
            today = datetime.now().date()
            month_start = datetime.now().replace(day=1).date()
            
            stats = {
                "plan_tier": plan_tier,
                "quotas": quotas,
                "usage": {
                    "daily": {},
                    "monthly": {},
                    "total": {}
                }
            }
            
            for media_type in ["image", "video", "text"]:
                # Daily usage
                daily_usage = db.execute(
                    select(func.count(MediaJob.id)).filter(
                        and_(
                            MediaJob.user_id == user_id,
                            MediaJob.media_type == media_type,
                            func.date(MediaJob.created_at) == today
                        )
                    )
                ).scalar() or 0
                
                # Monthly usage
                monthly_usage = db.execute(
                    select(func.count(MediaJob.id)).filter(
                        and_(
                            MediaJob.user_id == user_id,
                            MediaJob.media_type == media_type,
                            func.date(MediaJob.created_at) >= month_start
                        )
                    )
                ).scalar() or 0
                
                # Total usage
                total_usage = db.execute(
                    select(func.count(MediaJob.id)).filter(
                        and_(
                            MediaJob.user_id == user_id,
                            MediaJob.media_type == media_type
                        )
                    )
                ).scalar() or 0
                
                stats["usage"]["daily"][media_type] = daily_usage
                stats["usage"]["monthly"][media_type] = monthly_usage
                stats["usage"]["total"][media_type] = total_usage
            
            db.close()
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return {"error": str(e)}


# Global instance
quota_service = QuotaService()
