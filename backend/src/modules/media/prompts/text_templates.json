{"product_description": {"structure": {"hook": "Attention-grabbing opening line", "features": "Key features and specifications", "benefits": "Customer benefits and value proposition", "social_proof": "Trust signals and credibility", "call_to_action": "Clear next step for customer"}, "frameworks": {"AIDA": {"attention": "Compelling headline or opening", "interest": "Interesting features and details", "desire": "Benefits that create want", "action": "Clear call-to-action"}, "PAS": {"problem": "Problem the product solves", "agitation": "Consequences of not solving it", "solution": "How the product provides the solution"}, "features_benefits": {"features": "What the product has", "advantages": "What those features do", "benefits": "What that means for the customer"}}, "tone_variations": {"professional": "authoritative, trustworthy, expert", "friendly": "approachable, conversational, warm", "luxury": "sophisticated, exclusive, premium", "casual": "relaxed, informal, everyday", "energetic": "exciting, dynamic, enthusiastic"}}, "marketing_copy": {"ad_copy": {"headline_formulas": ["How to {achieve_benefit} with {product_title}", "The {adjective} {product_title} that {unique_benefit}", "{number} Reasons Why {product_title} is {superlative}", "Discover the Secret to {desired_outcome}", "Finally, a {product_category} that {solves_problem}"], "body_structures": {"problem_solution": "Identify problem → Present solution → Show benefits → Call to action", "story_driven": "Set scene → Introduce challenge → Present solution → Happy ending", "feature_focused": "Highlight key features → Explain benefits → Provide proof → Encourage action"}}, "social_captions": {"instagram": {"style": "visual-first, hashtag-friendly, engaging", "length": "125-150 characters for optimal engagement", "elements": ["emoji usage", "relevant hashtags", "call-to-action", "brand voice"]}, "facebook": {"style": "conversational, community-focused, shareable", "length": "40-80 characters for best reach", "elements": ["question to encourage engagement", "clear value proposition", "link to product"]}, "twitter": {"style": "concise, witty, trending", "length": "71-100 characters for optimal engagement", "elements": ["trending hashtags", "mention relevant accounts", "clear message"]}}}, "seo_content": {"meta_descriptions": {"template": "{primary_keyword} - {value_proposition}. {secondary_benefit}. {call_to_action}", "length": "150-160 characters", "elements": ["primary keyword", "compelling benefit", "call-to-action"]}, "product_titles": {"template": "{brand} {product_name} - {key_feature} | {category}", "seo_best_practices": ["include primary keyword", "under 60 characters", "compelling and descriptive"]}, "alt_text": {"template": "{product_name} {key_visual_elements} {context}", "guidelines": ["descriptive but concise", "include relevant keywords", "under 125 characters"]}}}