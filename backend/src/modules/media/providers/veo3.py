"""
Google Veo 3 Provider Plugin for E-commerce Video Generation.
Provides professional product video generation using Google's Veo 3 AI.
Specialized for product showcases, lifestyle videos, and social media content.
"""

import time
import logging
import asyncio
import os
from typing import Dict, List, Optional, Any

from google import genai
from google.genai import types

from .base import (
    MediaProviderPlugin,
    ProviderConfig,
    BaseMediaProvider
)
from ...core.config import get_settings
from ..schemas import (
    ProviderMediaRequest,
    MediaGenerationResult,
    ProductContext,
    ShopBranding,
    ContentStyle,
    TargetAudience,
    UsageContext
)
from ..engines.context_engine import context_engine
from ..engines.prompt_engine import prompt_engine, MediaType, PromptContext, Platform

logger = logging.getLogger(__name__)


class Veo3Provider(BaseMediaProvider):
    """Google Veo 3 provider plugin for video generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[ProviderConfig] = None
        self.model = "veo-3.0-fast-generate-001"

    @property
    def provider_name(self) -> str:
        return "veo3"

    @property
    def supported_media_types(self) -> List[str]:
        return ["video"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Veo 3 provider."""
        # Get API key from settings
        settings = self._get_settings()
        api_key = getattr(settings, "VEO3_API_KEY", None)

        if not api_key:
            logger.error("VEO3_API_KEY not configured in settings")
            return False

        try:
            # Additional Veo 3-specific initialization
            self.client = genai.Client(
                http_options={"api_version": "v1beta"},
                api_key=api_key
            )

            logger.info(f"Initialized Veo 3 provider with model: {self.model}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Veo 3 provider: {e}")
            return False

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce videos using Google Veo 3."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Generate professional video concepts using the prompt engine
            video_concepts = await self._generate_video_concepts(request)

            # Generate videos for each concept
            all_variants = []

            for i, concept in enumerate(video_concepts):
                # Extract generation settings
                settings = request.settings or {}
                custom_config = request.custom_config or {}

                # Create video config based on settings
                aspect_ratio = concept["aspect_ratio"]
                duration_seconds = concept.get("duration", 8)

                video_config = types.GenerateVideosConfig(
                    aspect_ratio=aspect_ratio.replace(":", "/"),  # Convert 16:9 to 16/9
                    number_of_videos=1,
                    duration_seconds=duration_seconds,
                    person_generation="ALLOW_ALL" if custom_config.get("allow_adult", False) else "ALLOW_ADULT",
                )

                # Generate video using Google GenAI SDK
                operation = self.client.models.generate_videos(
                    model=self.model,
                    prompt=concept["prompt"],
                    config=video_config,
                )

                # Wait for the video(s) to be generated
                while not operation.done:
                    logger.info("Video has not been generated yet. Check again in 10 seconds...")
                    time.sleep(10)
                    operation = self.client.operations.get(operation)

                result = operation.result
                if not result:
                    logger.error("Error occurred while generating video.")
                    continue

                generated_videos = result.generated_videos
                if not generated_videos:
                    logger.error("No videos were generated.")
                    continue

                logger.info(f"Generated {len(generated_videos)} video(s).")
                for n, generated_video in enumerate(generated_videos):
                    video_url = generated_video.video.uri
                    logger.info(f"Video has been generated: {video_url}")

                    # Download the video
                    try:
                        self.client.files.download(file=generated_video.video)
                        local_path = f"video_{i}_{n}.mp4"
                        generated_video.video.save(local_path)
                        logger.info(f"Video {video_url} has been downloaded to {local_path}.")
                    except Exception as e:
                        logger.warning(f"Failed to download video: {e}")
                        local_path = video_url  # Use remote URL if download fails

                    # Process generated video
                    all_variants.append({
                        "variant_name": concept["variant_name"],
                        "video_url": local_path if local_path != video_url else video_url,
                        "thumbnail_url": generated_video.video.uri.replace('.mp4', '_thumb.jpg'),  # Placeholder
                        "duration": duration_seconds,
                        "resolution": self._get_resolution_for_aspect(concept["aspect_ratio"]),
                        "aspect_ratio": concept["aspect_ratio"],
                        "style_type": concept["style_type"],
                        "prompt_used": concept["prompt"],
                        "generation_metadata": {
                            "provider": "veo3",
                            "model": self.model,
                            "concept_type": concept["concept_type"],
                            "target_audience": concept.get("target_audience"),
                            "platform_optimized": concept.get("platform")
                        }
                    })

                # Add delay between requests
                await asyncio.sleep(1.0)

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"veo3_batch_{hash(request.product_title)}",
                variants=all_variants[:request.variants_count],
                estimated_completion_time=len(video_concepts) * 180
            )

        except Exception as e:
            logger.error(f"Veo 3 generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_video_concepts(self, request: ProviderMediaRequest) -> List[Dict[str, Any]]:
        """Generate professional video concepts using the prompt engine."""
        concepts = []

        # Create product context from request
        product_context = request.product_context or self._create_product_context_from_request(request)

        # Define video concept variants
        concept_variants = [
            {
                "media_type": MediaType.PRODUCT_VIDEO,
                "concept_type": "product_showcase",
                "variant_name": "product_showcase",
                "aspect_ratio": "16:9",
                "duration": 30,
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_VIDEO,
                "concept_type": "lifestyle_integration",
                "variant_name": "lifestyle_video",
                "aspect_ratio": "9:16",
                "duration": 30,
                "platform": Platform.INSTAGRAM
            },
            {
                "media_type": MediaType.SOCIAL_VIDEO,
                "concept_type": "social_media",
                "variant_name": "social_video",
                "aspect_ratio": "1:1",
                "duration": 15,
                "platform": Platform.INSTAGRAM
            },
            {
                "media_type": MediaType.PRODUCT_VIDEO,
                "concept_type": "360_showcase",
                "variant_name": "360_rotation",
                "aspect_ratio": "1:1",
                "duration": 20,
                "platform": None
            }
        ]

        # Limit concepts based on requested variants
        concept_variants = concept_variants[:request.variants_count]

        for variant in concept_variants:
            try:
                # Create prompt context
                prompt_context = PromptContext(
                    media_type=variant["media_type"],
                    platform=variant.get("platform"),
                    aspect_ratio=variant["aspect_ratio"],
                    duration_seconds=variant["duration"],
                    style_preference=request.content_style,
                    campaign_theme=request.campaign_theme,
                    call_to_action=request.call_to_action
                )

                # Generate professional prompt
                generated_prompt = await prompt_engine.generate_prompt(
                    product_context=product_context,
                    prompt_context=prompt_context,
                    brand_context=self._create_brand_context_from_request(request)
                )

                concepts.append({
                    "prompt": generated_prompt.main_prompt,
                    "concept_type": variant["concept_type"],
                    "variant_name": variant["variant_name"],
                    "aspect_ratio": variant["aspect_ratio"],
                    "duration": variant["duration"],
                    "style_type": self._get_style_type_for_concept(variant["concept_type"]),
                    "camera_movements": self._get_camera_movements_for_concept(variant["concept_type"]),
                    "lighting": self._get_lighting_for_concept(variant["concept_type"]),
                    "music_style": self._get_music_style_for_concept(variant["concept_type"]),
                    "target_audience": [aud.value for aud in (product_context.target_audience or [])],
                    "platform": variant.get("platform").value if variant.get("platform") else None,
                    "quality_score": generated_prompt.estimated_quality_score
                })

            except Exception as e:
                logger.warning(f"Failed to generate concept for variant {variant['variant_name']}: {e}")
                # Fallback to simple concept
                concepts.append(self._create_fallback_concept(request, variant))

        return concepts

    def _get_style_type_for_concept(self, concept_type: str) -> str:
        """Get style type for video concept."""
        style_map = {
            "product_showcase": "professional_showcase",
            "lifestyle_integration": "lifestyle_natural",
            "social_media": "dynamic_engaging",
            "360_showcase": "product_rotation"
        }
        return style_map.get(concept_type, "professional")

    def _get_camera_movements_for_concept(self, concept_type: str) -> str:
        """Get camera movements for video concept."""
        movement_map = {
            "product_showcase": "smooth zoom and pan",
            "lifestyle_integration": "handheld natural movement",
            "social_media": "dynamic quick cuts",
            "360_showcase": "360-degree rotation"
        }
        return movement_map.get(concept_type, "smooth")

    def _get_lighting_for_concept(self, concept_type: str) -> str:
        """Get lighting setup for video concept."""
        lighting_map = {
            "product_showcase": "professional studio lighting",
            "lifestyle_integration": "natural ambient lighting",
            "social_media": "bright vibrant lighting",
            "360_showcase": "even diffused lighting"
        }
        return lighting_map.get(concept_type, "professional")

    def _get_music_style_for_concept(self, concept_type: str) -> str:
        """Get music style for video concept."""
        music_map = {
            "product_showcase": "upbeat corporate",
            "lifestyle_integration": "ambient lifestyle",
            "social_media": "trendy upbeat",
            "360_showcase": "minimal ambient"
        }
        return music_map.get(concept_type, "upbeat")



    def _create_fallback_concept(self, request: ProviderMediaRequest, variant: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback concept when prompt engine fails."""
        base_prompt = f"Professional {variant['concept_type']} video of {request.product_title}"

        if request.product_description:
            base_prompt += f". {request.product_description[:100]}"

        base_prompt += f". High-quality {variant['duration']}-second video with smooth camera movements and professional presentation."

        return {
            "prompt": base_prompt,
            "concept_type": variant["concept_type"],
            "variant_name": variant["variant_name"],
            "aspect_ratio": variant["aspect_ratio"],
            "duration": variant["duration"],
            "style_type": self._get_style_type_for_concept(variant["concept_type"]),
            "camera_movements": self._get_camera_movements_for_concept(variant["concept_type"]),
            "lighting": self._get_lighting_for_concept(variant["concept_type"]),
            "music_style": self._get_music_style_for_concept(variant["concept_type"]),
            "quality_score": 0.7
        }


    def _create_video_prompt(self, request: ProviderMediaRequest) -> str:
        """Create optimized prompt for Veo 3."""
        base_prompt = f"Create a professional product video showcasing {request.product_title}."

        if request.product_description:
            base_prompt += f" Product details: {request.product_description[:200]}"

        base_prompt += " The video should be high-quality, well-lit, with smooth camera movements and professional presentation."

        if request.template_id:
            template_styles = {
                "modern_product_showcase": "modern, clean aesthetic with smooth transitions",
                "dynamic_lifestyle": "dynamic, energetic with lifestyle integration",
                "minimalist_clean": "minimalist, elegant with focus on product details",
                "luxury_premium": "luxury, sophisticated with premium feel"
            }
            style = template_styles.get(request.template_id, "professional product showcase")
            base_prompt += f" Style: {style}."

        return base_prompt




    async def download_media(self, media_url: str) -> bytes:
        """Download video from Veo 3."""
        if not self.client:
            raise ValueError("Provider not initialized")

        # For local files, read from disk
        if media_url.startswith("/") or media_url.startswith("./"):
            with open(media_url, "rb") as f:
                return f.read()

        # For remote URLs, download
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(media_url)
            response.raise_for_status()
            return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Veo 3 provider information."""
        video_config = self.config.metadata['video_generation']

        return {
            "name": "Google Veo 3 - Video Generation",
            "description": "Professional e-commerce video generation using Google's Veo 3 AI",
            "supported_formats": ["video"],
            "models": [self.model],
            "max_variants_per_request": 4,
            "supported_aspect_ratios": video_config['supported_aspect_ratios'],
            "max_duration_seconds": video_config['max_duration_seconds'],
            "supported_person_generation": video_config['person_generation_modes'],
            "features": [
                "Professional product videos",
                "Lifestyle video generation",
                "Social media optimized videos",
                "Brand-consistent styling",
                "Multi-variant generation",
                "High-quality AI generation"
            ],
            "estimated_cost_per_video": self.config.metadata.get('cost_per_video', 1.0),
            "average_generation_time": 180,
            "quality_score": 0.95
        }

    async def cleanup(self) -> None:
        """Cleanup Veo 3 provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Veo 3 provider")