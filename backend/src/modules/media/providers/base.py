"""
Base Provider Class for Media Generation Providers.
Contains common functionality shared across all media providers.
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from pydantic import BaseModel

from ..schemas import ProviderMediaRequest, ProductContext, ShopBranding, ProductCategory, ContentStyle
from ...core.config import get_settings
from ..common.language_utils import get_language_instruction

logger = logging.getLogger(__name__)


class ProviderConfig(BaseModel):
    """Configuration for a media provider."""
    name: str
    base_url: Optional[str] = None
    timeout: int = 300
    max_retries: int = 3
    rate_limit: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class MediaProviderPlugin(ABC):
    """Abstract base class for media provider plugins."""

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Name of the provider."""
        pass

    @property
    @abstractmethod
    def supported_media_types(self) -> List[str]:
        """List of supported media types (image, video, voice)."""
        pass

    @abstractmethod
    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the provider with configuration."""
        pass

    @abstractmethod
    async def generate_media(self, request: ProviderMediaRequest) -> 'ProviderMediaResult':
        """Generate media using the provider."""
        pass

    @abstractmethod
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status from the provider."""
        pass

    @abstractmethod
    async def download_media(self, media_url: str) -> bytes:
        """Download media content from the provider."""
        pass

    @abstractmethod
    async def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information and capabilities."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup provider resources."""
        pass




class BaseMediaProvider(MediaProviderPlugin, ABC):
    """Base class for all media generation providers with common functionality."""

    def __init__(self):
        self.config: Optional[Any] = None
        self.model: Optional[str] = None

    def _create_product_context_from_request(self, request: ProviderMediaRequest) -> ProductContext:
        """Create product context from request data - common across all providers."""
        from ..context_engine import ProductContext

        # Use category from request's product_context if available, otherwise default
        category = ProductCategory.FASHION_APPAREL  # Default fallback
        if request.product_context and request.product_context.category:
            category = request.product_context.category

        return ProductContext(
            title=request.product_title,
            description=request.product_description or "",
            category=category,
            target_audience=request.target_audience or [],
            key_features=request.product_context.key_features if request.product_context else None,
            materials=request.product_context.materials if request.product_context else None,
            colors=request.product_context.colors if request.product_context else None,
            price_tier=request.product_context.price_tier if request.product_context else None,
            style_keywords=request.product_context.style_keywords if request.product_context else None,
            usage_context=getattr(request, 'usage_context', None) or []
        )

    def _create_brand_context_from_request(self, request: ProviderMediaRequest) -> Optional['BrandContext']:
        """Create brand context from request data - common across all providers."""
        if not request.shop_branding:
            return None

        from ..context_engine import BrandContext

        return BrandContext(
            name=request.shop_branding.shop_name or "Brand",
            brand_voice=request.shop_branding.brand_voice,
            brand_values=request.shop_branding.brand_values or [],
            visual_style=getattr(request, 'visual_style', None) or request.shop_branding.visual_style,
            color_palette=request.shop_branding.color_palette or [],
            industry="e-commerce"  # Default for media providers
        )

    def _get_max_tokens_for_content_type(self, content_type: str) -> int:
        """Get maximum tokens for different content types from configuration."""
        if not self.config or 'text_generation' not in self.config.metadata:
            # Default token limits
            token_limits = {
                "product_description": 300,
                "marketing_copy": 400,
                "social_caption": 100,
                "seo_snippet": 200
            }
            return token_limits.get(content_type, 300)

        token_limits = self.config.metadata['text_generation']['token_limits']
        return token_limits.get(content_type, token_limits.get('default', 300))

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio from configuration."""
        if not self.config or 'image_generation' not in self.config.metadata:
            # Default aspect ratios
            default_ratios = {
                "1:1": {"width": 1024, "height": 1024},
                "16:9": {"width": 1024, "height": 576},
                "9:16": {"width": 576, "height": 1024},
                "4:5": {"width": 768, "height": 960}
            }
            return default_ratios.get(aspect_ratio, {"width": 1024})["width"]

        image_config = self.config.metadata['image_generation']
        aspect_ratios = image_config['aspect_ratios']
        return aspect_ratios.get(aspect_ratio, {}).get('width', 1024)

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio from configuration."""
        if not self.config or 'image_generation' not in self.config.metadata:
            # Default aspect ratios
            default_ratios = {
                "1:1": {"width": 1024, "height": 1024},
                "16:9": {"width": 1024, "height": 576},
                "9:16": {"width": 576, "height": 1024},
                "4:5": {"width": 768, "height": 960}
            }
            return default_ratios.get(aspect_ratio, {"height": 1024})["height"]

        image_config = self.config.metadata['image_generation']
        aspect_ratios = image_config['aspect_ratios']
        return aspect_ratios.get(aspect_ratio, {}).get('height', 1024)

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio from configuration."""
        if not self.config or 'video_generation' not in self.config.metadata:
            # Default resolutions
            default_resolutions = {
                "1:1": "1024x1024",
                "16:9": "1920x1080",
                "9:16": "1080x1920",
                "4:5": "768x960"
            }
            return default_resolutions.get(aspect_ratio, "1920x1080")

        video_config = self.config.metadata['video_generation']
        aspect_resolutions = video_config['aspect_resolutions']
        return aspect_resolutions.get(aspect_ratio, "1920x1080")

    def _get_aspect_ratio_for_variant(self, variant_name: str) -> str:
        """Get aspect ratio for variant from configuration."""
        if not self.config or 'video_generation' not in self.config.metadata:
            # Default variant ratios
            default_variants = {
                "square": "1:1",
                "vertical": "9:16",
                "horizontal": "16:9",
                "story": "9:16"
            }
            return default_variants.get(variant_name, "1:1")

        video_config = self.config.metadata['video_generation']
        variant_ratios = video_config['variant_aspect_ratios']
        return variant_ratios.get(variant_name, "1:1")

    def _get_language_instruction(self, language: str, fallback_language: str) -> str:
        """Get language-specific instructions for text generation."""
        from ..common.language_utils import get_language_instruction
        return get_language_instruction(language, fallback_language)

    def _get_settings(self):
        """Get centralized settings."""
        return get_settings()

    def _validate_provider_config(self, config: ProviderConfig) -> tuple[bool, Optional[str]]:
        """
        Validate provider configuration.

        Args:
            config: Provider configuration to validate

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not config.name:
            return False, "Provider name is required"

        if not config.base_url and not hasattr(config, 'metadata'):
            return False, "Either base_url or metadata must be provided"

        # Check for required metadata based on provider type
        if hasattr(config, 'metadata') and config.metadata:
            # Validate common metadata structure
            required_keys = ['timeout', 'max_retries']
            for key in required_keys:
                if key not in config.metadata:
                    return False, f"Missing required metadata key: {key}"

        return True, None

    def _get_provider_client_config(self, config: ProviderConfig) -> dict:
        """
        Extract client configuration from provider config.

        Args:
            config: Provider configuration

        Returns:
            Dictionary with client configuration
        """
        client_config = {
            'timeout': config.timeout,
            'max_retries': config.max_retries,
        }

        if config.rate_limit:
            client_config['rate_limit'] = config.rate_limit

        if hasattr(config, 'metadata') and config.metadata:
            # Add any additional client config from metadata
            client_config.update(config.metadata.get('client_config', {}))

        return client_config

    def _setup_provider_logging(self) -> logging.Logger:
        """
        Setup standardized logging for this provider.

        Returns:
            Configured logger instance
        """
        logger_name = f"media.providers.{self.provider_name}"
        provider_logger = logging.getLogger(logger_name)

        # Ensure logger has appropriate level
        if not provider_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                f'%(asctime)s - {self.provider_name} - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            provider_logger.addHandler(handler)
            provider_logger.setLevel(logging.INFO)

        return provider_logger