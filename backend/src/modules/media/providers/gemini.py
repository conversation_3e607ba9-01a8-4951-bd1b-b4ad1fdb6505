"""
Gemini AI Provider Plugin for Text Generation.
Provides comprehensive text generation and content creation using Google's Gemini AI.
Specialized for product descriptions, marketing copy, SEO content, and social media captions.
"""

import logging
from typing import Dict, List, Optional, Any

import httpx
from google import genai
from google.genai import types

from .base import (
    MediaProviderPlugin,
    ProviderConfig,
    BaseMediaProvider
)
from ..schemas import (
    ProviderMediaRequest,
    ProviderMediaResult,
    ProductContext,
    ShopBranding
)
from ..engines.prompt_engine import prompt_engine
from ...core.config import get_settings

logger = logging.getLogger(__name__)


class GeminiProvider(BaseMediaProvider):
    """Gemini AI provider plugin for text generation and content creation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.http_client: Optional[httpx.AsyncClient] = None
        self.config: Optional[ProviderConfig] = None
        self.model = None  # Will be set from config during initialization


    @property
    def provider_name(self) -> str:
        return "gemini"

    @property
    def supported_media_types(self) -> List[str]:
        return ["text"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Gemini provider."""
        # Get API key from settings
        settings = self._get_settings()
        api_key = getattr(settings, "GEMINI_API_KEY", None)

        if not api_key:
            logger.error("GEMINI_API_KEY not configured in settings")
            return False

        try:
            # Additional Gemini-specific initialization
            import httpx
            self.http_client = httpx.AsyncClient(timeout=config.timeout)

            # Get model from config - required, no fallback
            text_gen_config = config.metadata['text_generation']
            self.model = text_gen_config['model']

            logger.info(f"Initialized Gemini provider with model: {self.model}")
            return True

        except KeyError as e:
            logger.error(f"Missing required configuration: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to initialize Gemini provider: {e}")
            return False

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate text content using Gemini AI."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            if request.media_type == "text":
                return await self._generate_text_content(request)
            else:
                return ProviderMediaResult(
                    success=False,
                    error_message=f"Unsupported media type: {request.media_type}. This provider only supports text generation."
                )

        except Exception as e:
            logger.error(f"Gemini text generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_text_content(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce text content using Gemini."""
        try:
            # Get content types from configuration - required, no fallback
            text_gen_config = self.config.metadata['text_generation']
            content_types = text_gen_config['content_types']

            generated_content = []

            for content_type in content_types:
                # Use prompt_engine to generate the prompt
                product_context = request.product_context or self._create_product_context_from_request(request)
                brand_context = self._create_brand_context_from_request(request)

                prompt = await prompt_engine.generate_text_prompt(
                    product_context=product_context,
                    content_type=content_type,
                    brand_context=brand_context,
                    language=getattr(request, 'language', 'en'),
                    fallback_language=getattr(request, 'fallback_language', 'en')
                )

                contents = [types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)]
                )]

                # Get temperature from configuration - required, no fallback
                temp_settings = text_gen_config['temperature_settings']
                temperature = temp_settings.get(content_type, temp_settings['default'])

                config = types.GenerateContentConfig(
                    temperature=temperature,
                    max_output_tokens=self._get_max_tokens_for_content_type(content_type),
                )

                response = self.client.models.generate_content(
                    model=self.model,
                    contents=contents,
                    config=config
                )

                generated_text = response.text
                generated_content.append({
                    "content_type": content_type,
                    "text": generated_text.strip(),
                    "word_count": len(generated_text.split()),
                    "character_count": len(generated_text),
                    "variant_name": f"{content_type}_variant"
                })

            # Get estimated completion time from config - required, no fallback
            time_per_variant = text_gen_config['estimated_completion_time_per_variant']

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"gemini_text_{hash(request.product_title)}",
                variants=generated_content,
                estimated_completion_time=len(content_types) * time_per_variant
            )

        except Exception as e:
            logger.error(f"Error generating text with Gemini: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )





    async def download_media(self, media_url: str) -> bytes:
        """Download media from Gemini."""
        if not self.http_client:
            raise ValueError("Provider not initialized")

        response = await self.http_client.get(media_url)
        response.raise_for_status()
        return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Gemini provider information."""
        text_gen_config = self.config.metadata['text_generation']
        provider_info_config = self.config.metadata['provider_info']

        return {
            "name": provider_info_config['name'],
            "description": provider_info_config['description'],
            "supported_formats": ["text"],
            "models": [self.model],
            "max_text_variants_per_request": text_gen_config['max_variants_per_request'],
            "supported_content_types": text_gen_config['content_types'],
            "supported_languages": text_gen_config['supported_languages'],
            "features": provider_info_config['features'],
            "estimated_cost_per_request": provider_info_config['estimated_cost_per_request'],
            "average_generation_time": provider_info_config['average_generation_time'],
            "quality_score": text_gen_config['quality_score']
        }

    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            self.client = None
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info("Cleaned up Gemini provider")