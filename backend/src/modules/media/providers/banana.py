"""
Google Gemini Image Provider Plugin for E-commerce Media Generation.
Provides professional product image generation using Google's Gemini 2.5 Flash Image Preview.
Specialized for e-commerce product photography, lifestyle images, and promotional content.
"""

import asyncio
import base64
import logging
import mimetypes
import os
from typing import Dict, List, Optional, Any

from google import genai
from google.genai import types

from .base import (
    MediaProviderPlugin,
    ProviderConfig,
    BaseMediaProvider
)
from ...core.config import get_settings
from ..schemas import (
    ProviderMediaRequest,
    MediaGenerationResult,
    ProductContext,
    ShopBranding,
    ProductCategory,
    ContentStyle,
    TargetAudience,
    UsageContext
)
from ..engines.context_engine import context_engine
from ..engines.prompt_engine import prompt_engine, MediaType, PromptContext, Platform

logger = logging.getLogger(__name__)


class BananaProvider(BaseMediaProvider):
    """Google Gemini provider plugin for image generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[ProviderConfig] = None
        self.model = "gemini-2.5-flash-image-preview"

    @property
    def provider_name(self) -> str:
        return "banana"

    @property
    def supported_media_types(self) -> List[str]:
        return ["image"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Banana provider."""
        # Get API key from settings
        settings = self._get_settings()
        api_key = settings.BANANA_API_KEY

        if not api_key:
            logger.error("BANANA_API_KEY not configured in settings")
            return False


    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce images using Google Gemini."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Generate professional prompts using the prompt engine
            prompts = await self._generate_professional_prompts(request)

            # Generate images for each prompt variant
            all_images = []

            for i, prompt_data in enumerate(prompts):
                # Create content for Gemini
                contents = [
                    types.Content(
                        role="user",
                        parts=[
                            types.Part.from_text(text=prompt_data["prompt"]),
                        ],
                    ),
                ]

                generate_content_config = types.GenerateContentConfig(
                    response_modalities=[
                        "IMAGE",
                        "TEXT",
                    ],
                )

                # Generate image using streaming
                image_data = None
                file_index = 0

                for chunk in self.client.models.generate_content_stream(
                    model=self.model,
                    contents=contents,
                    config=generate_content_config,
                ):
                    if (
                        chunk.candidates is None
                        or chunk.candidates[0].content is None
                        or chunk.candidates[0].content.parts is None
                    ):
                        continue

                    if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                        inline_data = chunk.candidates[0].content.parts[0].inline_data
                        data_buffer = inline_data.data

                        # Convert to base64 data URL
                        file_extension = mimetypes.guess_extension(inline_data.mime_type) or ".png"
                        base64_data = base64.b64encode(data_buffer).decode("utf-8")
                        data_url = f"data:{inline_data.mime_type};base64,{base64_data}"

                        image_data = {
                            "image_url": data_url,
                            "thumbnail_url": data_url,
                            "width": self._get_width_for_aspect(request.aspect_ratio),
                            "height": self._get_height_for_aspect(request.aspect_ratio),
                            "style": prompt_data["style"],
                            "variant_name": prompt_data["variant_name"],
                            "prompt_used": prompt_data["prompt"],
                            "generation_metadata": {
                                "provider": "gemini",
                                "model": self.model,
                                "style_type": prompt_data["style_type"],
                                "target_audience": prompt_data.get("target_audience"),
                                "usage_context": prompt_data.get("usage_context")
                            }
                        }
                        break

                if image_data:
                    all_images.append(image_data)

                # Add small delay between requests to respect rate limits
                await asyncio.sleep(0.5)

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"gemini_batch_{hash(request.product_title)}",
                images=all_images[:request.num_images],  # Limit to requested number
                estimated_completion_time=len(prompts) * 30
            )

        except Exception as e:
            logger.error(f"Gemini image generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_professional_prompts(self, request: ProviderMediaRequest) -> List[Dict[str, Any]]:
        """Generate professional prompts using the prompt engine for different image styles."""
        prompts = []

        # If custom prompt is provided, use it
        if request.custom_prompt:
            return [{
                "prompt": request.custom_prompt,
                "style": request.style or "custom",
                "variant_name": "custom",
                "style_type": "custom"
            }]

        # Create product context from request
        from ..common.context_creation import create_product_context
        product_context = request.product_context or create_product_context(request)

        # Generate different style variants
        style_variants = [
            {
                "media_type": MediaType.PRODUCT_PHOTOGRAPHY,
                "style_type": "product_photography",
                "variant_name": "product_shot",
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_PHOTOGRAPHY,
                "style_type": "lifestyle",
                "variant_name": "lifestyle_shot",
                "platform": Platform.INSTAGRAM
            },
            {
                "media_type": MediaType.PRODUCT_PHOTOGRAPHY,
                "style_type": "minimalist",
                "variant_name": "minimalist_shot",
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_PHOTOGRAPHY,
                "style_type": "social_media",
                "variant_name": "social_shot",
                "platform": Platform.INSTAGRAM
            }
        ]

        # Limit variants based on requested number
        style_variants = style_variants[:request.num_images]

        for variant in style_variants:
            try:
                # Create prompt context
                prompt_context = PromptContext(
                    media_type=variant["media_type"],
                    platform=variant.get("platform"),
                    aspect_ratio=request.aspect_ratio,
                    style_preference=request.content_style,
                    campaign_theme=request.campaign_theme,
                    call_to_action=request.call_to_action
                )

                # Generate professional prompt
                from ..common.context_creation import create_brand_context
                brand_context = create_brand_context(request)
                generated_prompt = await prompt_engine.generate_prompt(
                    product_context=product_context,
                    prompt_context=prompt_context,
                    brand_context=brand_context
                )

                prompts.append({
                    "prompt": generated_prompt.main_prompt,
                    "negative_prompt": generated_prompt.negative_prompt,
                    "style": variant["style_type"],
                    "variant_name": variant["variant_name"],
                    "style_type": variant["style_type"],
                    "target_audience": [aud.value for aud in (product_context.target_audience or [])],
                    "usage_context": [ctx.value for ctx in (request.usage_context or [])],
                    "quality_score": generated_prompt.estimated_quality_score
                })

            except Exception as e:
                logger.warning(f"Failed to generate prompt for variant {variant['variant_name']}: {e}")
                # Fallback to simple prompt
                prompts.append(self._create_fallback_prompt(request, variant))

        return prompts



    def _create_fallback_prompt(self, request: ProviderMediaRequest, variant: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback prompt when prompt engine fails."""
        base_prompt = f"Professional {variant['style_type']} photograph of {request.product_title}"

        if request.product_description:
            base_prompt += f", {request.product_description[:100]}"

        style_additions = {
            "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }

        style_addition = style_additions.get(variant["style_type"], "professional photography")
        base_prompt += f", {style_addition}, 8K resolution, sharp focus, professional lighting"

        return {
            "prompt": base_prompt,
            "negative_prompt": "blurry, low quality, distorted, oversaturated, poor lighting, amateur",
            "style": variant["style_type"],
            "variant_name": variant["variant_name"],
            "style_type": variant["style_type"],
            "quality_score": 0.7
        }




    async def download_media(self, media_url: str) -> bytes:
        """Download media from Gemini."""
        if not self.client:
            raise ValueError("Provider not initialized")

        # For data URLs, extract the binary data
        if media_url.startswith("data:"):
            header, encoded = media_url.split(",", 1)
            return base64.b64decode(encoded)

        # For regular URLs, download
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(media_url)
            response.raise_for_status()
            return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Gemini provider information."""
        image_config = self.config.metadata['image_generation']
        aspect_ratios = image_config['aspect_ratios']

        return {
            "name": "Google Gemini - Image Generation",
            "description": "Professional e-commerce image generation using Gemini 2.5 Flash Image Preview",
            "supported_formats": ["image"],
            "models": [self.model],
            "max_images_per_request": image_config.get('max_images_per_request', 4),
            "supported_aspect_ratios": list(aspect_ratios.keys()),
            "supported_styles": [
                "product_photography",
                "lifestyle",
                "minimalist",
                "luxury",
                "social_media"
            ],
            "supported_categories": [
                "fashion_apparel",
                "footwear",
                "accessories",
                "beauty_cosmetics",
                "jewelry",
                "electronics",
                "home_decor"
            ],
            "features": [
                "Professional product photography",
                "Lifestyle scene generation",
                "Brand-consistent styling",
                "Multi-variant generation",
                "Context-aware prompting",
                "E-commerce optimization",
                "High-quality AI generation"
            ],
            "estimated_cost_per_image": self.config.metadata.get('cost_per_image', 0.04),
            "average_generation_time": 30,
            "quality_score": 0.95
        }

    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Gemini provider")