"""
ProductVideo platform data models.
Multi-tenant models for video generation, analytics, and billing.
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from core.db.database import Base


class MediaJobStatus(PyEnum):
    """Media job status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MediaVariantStatus(PyEnum):
    """Media variant status enumeration."""
    GENERATING = "generating"
    READY = "ready"
    FAILED = "failed"
    PROCESSING = "processing"


class PushStatus(PyEnum):
    """Push status enumeration."""
    PENDING = "pending"
    PUSHING = "pushing"
    COMPLETED = "completed"
    FAILED = "failed"


class PlanTier(PyEnum):
    """Billing plan tiers."""
    FREE = "free"
    STARTER = "starter"
    GROWTH = "growth"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class ImageProvider(PyEnum):
    """Image generation providers."""
    MOCK = "mock"
    BANANA = "banana"
    DALL_E = "dall_e"
    MIDJOURNEY = "midjourney"


class VideoProvider(PyEnum):
    """Video generation providers."""
    MOCK = "mock"
    VEO3 = "veo3"
    REVID_AI = "revid_ai"
    VIDIFY = "vidify"
    PRODUCT_STUDIO = "product_studio"


class BaseMediaProvider:
    """Base class for media generation providers."""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None  # httpx.AsyncClient or similar

    async def generate(self, request):
        """Generate media. To be implemented by subclasses."""
        raise NotImplementedError

    async def get_job_status(self, job_id: str):
        """Get job status. To be implemented by subclasses."""
        raise NotImplementedError

    async def download_media(self, media_url: str):
        """Download media. To be implemented by subclasses."""
        raise NotImplementedError


class MediaJob(Base):
    """
    Media generation job tracking.
    Each job represents a request to generate media for a product.
    """
    __tablename__ = 'media_jobs'

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)
    product_id = Column(BigInteger, nullable=False)  # External product ID from e-commerce platform (e.g., Shopify product ID)
    status = Column(Enum(MediaJobStatus), default=MediaJobStatus.PENDING, nullable=False)
    media_type = Column(String, nullable=False) # 'video', 'image', 'voice'
    provider = Column(String, nullable=False)  # revid_ai, vidify, product_studio

    # Job configuration
    template_id = Column(String, nullable=True)
    custom_config = Column(JSON, nullable=True)

    # Voice and text generation
    voice_id = Column(String, nullable=True)
    script = Column(Text, nullable=True)

    # Full payload storage for complex requests
    full_payload = Column(JSON, nullable=True)  # Store complete request payload
    job_id = Column(String, nullable=True)  # External job ID for tracking

    # Additional fields from schemas
    mode = Column(String, nullable=True)  # 'image', 'video'
    model = Column(String, nullable=True)  # Provider model name
    settings = Column(JSON, nullable=True)  # GenerationSettings as JSON
    items = Column(JSON, nullable=True)  # List of ProductItem as JSON
    shop_id = Column(BigInteger, nullable=True)  # Shop identifier
    product_ids = Column(JSON, nullable=True)  # List of product IDs as JSON
    celery_task_id = Column(String, nullable=True)  # Celery task ID

    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)

    # Idempotency and versioning
    idempotency_key = Column(String(32), nullable=True, index=True)  # For deduplication
    product_version = Column(String(16), nullable=True)  # Product data version hash

    # Quality and review
    needs_manual_review = Column(Boolean, default=False)
    qa_metadata = Column(JSON, nullable=True)

    # Localization
    language = Column(String(10), default="en")
    fallback_language = Column(String(10), default="en")

    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="media_jobs")

    def __repr__(self):
        return f"<MediaJob(id={self.id}, product_id='{self.product_id}', status='{self.status.value}')>"


class MediaVariant(Base):
    """
    Individual media variants generated from a job.
    A single job can produce multiple media variants.
    """
    __tablename__ = 'media_variants'

    id = Column(BigInteger, primary_key=True, index=True)
    job_id = Column(BigInteger, ForeignKey('media_jobs.id'), nullable=False)
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)  # Added for filtering
    external_variant_id = Column(String, nullable=True)  # External ID for API
    variant_name = Column(String, nullable=False)  # e.g., "square", "vertical", "horizontal"
    status = Column(Enum(MediaVariantStatus), default=MediaVariantStatus.GENERATING, nullable=False)

    # Media details
    duration_seconds = Column(Float, nullable=True)
    resolution = Column(String, nullable=True)  # e.g., "1080x1920"
    file_size_bytes = Column(Integer, nullable=True)

    # Storage URLs
    video_url = Column(String, nullable=True)
    image_url = Column(String, nullable=True)
    thumbnail_url = Column(String, nullable=True)

    # Provider-specific data
    provider_media_id = Column(String, nullable=True)
    provider_metadata = Column(JSON, nullable=True)

    # User interaction
    is_favorite = Column(Boolean, default=False)
    user_rating = Column(Integer, nullable=True)  # 1-5 stars

    # Platform integration
    push_status = Column(Enum(PushStatus), default=PushStatus.PENDING, nullable=False)
    media_id = Column(String, nullable=True)
    pushed_at = Column(DateTime(timezone=True), nullable=True)
    push_error_message = Column(Text, nullable=True)

    # Accessibility and content
    alt_text = Column(Text, nullable=True)  # Auto-generated alt text
    captions = Column(Text, nullable=True)  # Auto-generated captions/subtitles
    text_content = Column(Text, nullable=True)  # Generated text content

    # Quality and review
    quality_score = Column(Float, nullable=True)  # AI-generated quality score (0-100)
    needs_manual_review = Column(Boolean, default=False)
    qa_metadata = Column(JSON, nullable=True)

    # Legal and safety
    brand_safety_checked = Column(Boolean, default=False)
    copyright_validated = Column(Boolean, default=False)
    content_flags = Column(JSON, nullable=True)  # List of content flags

    # Error handling
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<MediaVariant(id={self.id}, variant_name='{self.variant_name}', status='{self.status.value}')>"


class Template(Base):
    """
    Video templates for different use cases.
    Templates define the structure and style of generated videos.
    """
    __tablename__ = 'templates'

    id = Column(BigInteger, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    provider = Column(String, nullable=False)  # Which provider this template belongs to
    
    # Template configuration
    template_config = Column(JSON, nullable=False)  # Provider-specific template data
    preview_url = Column(String, nullable=True)
    
    # Categorization
    category = Column(String, nullable=True)  # e.g., "product_showcase", "testimonial"
    tags = Column(JSON, nullable=True)  # Array of tags for filtering
    
    # Availability
    is_active = Column(Boolean, default=True)
    plan_tier_required = Column(Enum(PlanTier), default=PlanTier.FREE, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Template(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class GenerationBatch(Base):
    """
    Media generation batch tracking for media studio.
    Each batch represents a group of generation requests.
    """
    __tablename__ = 'generation_batches'

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)
    mode = Column(String, nullable=False)  # 'image' or 'video'
    aspect_ratio = Column(String, nullable=True)
    quality = Column(String, nullable=True)
    model = Column(String, nullable=False)
    requested_count = Column(Integer, default=0)
    completed_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    status = Column(String, default="pending")  # pending, processing, completed, failed

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="generation_batches")
    requests = relationship("GenerationRequest", back_populates="batch")

    def __repr__(self):
        return f"<GenerationBatch(id='{self.id}', mode='{self.mode}', status='{self.status}')>"


class GenerationRequest(Base):
    """
    Individual generation requests within a batch.
    """
    __tablename__ = 'generation_requests'

    id = Column(BigInteger, primary_key=True, index=True)
    batch_id = Column(BigInteger, ForeignKey('generation_batches.id'), nullable=False)
    product_id = Column(BigInteger, nullable=False)
    variant_id = Column(String, nullable=True)
    prompt = Column(Text, nullable=False)
    params_json = Column(JSON, nullable=True)
    status = Column(String, default="pending")  # pending, processing, completed, failed
    result_url = Column(String, nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    batch = relationship("GenerationBatch", back_populates="requests")


class MediaReview(Base):
    """
    Manual review tracking for generated media.
    Stores review decisions and feedback for quality control.
    """
    __tablename__ = 'media_reviews'

    id = Column(BigInteger, primary_key=True, index=True)
    variant_id = Column(BigInteger, ForeignKey('media_variants.id'), nullable=False)
    reviewer_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)

    # Review decision
    status = Column(String, nullable=False)  # approved, rejected, needs_revision
    quality_score = Column(Float, nullable=True)  # Manual quality score (0-100)

    # Feedback
    feedback = Column(Text, nullable=True)
    rejection_reasons = Column(JSON, nullable=True)  # List of rejection reasons
    suggested_improvements = Column(Text, nullable=True)

    # Review metadata
    review_criteria = Column(JSON, nullable=True)  # Criteria used for review
    review_time_seconds = Column(Integer, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<MediaReview(id={self.id}, variant_id={self.variant_id}, status='{self.status}')>"


class RejectedAsset(Base):
    """
    Tracking for rejected media assets that need regeneration.
    Stores rejection history and regeneration attempts.
    """
    __tablename__ = 'rejected_assets'

    id = Column(BigInteger, primary_key=True, index=True)
    original_variant_id = Column(BigInteger, ForeignKey('media_variants.id'), nullable=False)
    job_id = Column(BigInteger, ForeignKey('media_jobs.id'), nullable=False)
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)

    # Rejection details
    rejection_reason = Column(String, nullable=False)
    rejection_category = Column(String, nullable=True)  # quality, brand_safety, legal, etc.
    rejection_details = Column(JSON, nullable=True)

    # Original asset info
    original_media_url = Column(String, nullable=True)
    original_prompt = Column(Text, nullable=True)
    original_settings = Column(JSON, nullable=True)

    # Regeneration tracking
    regeneration_attempts = Column(Integer, default=0)
    last_regeneration_at = Column(DateTime(timezone=True), nullable=True)
    regenerated_variant_id = Column(BigInteger, ForeignKey('media_variants.id'), nullable=True)

    # Status
    status = Column(String, default="pending_regeneration")  # pending_regeneration, regenerating, resolved

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<RejectedAsset(id={self.id}, reason='{self.rejection_reason}', status='{self.status}')>"

    def __repr__(self):
        return f"<GenerationRequest(id='{self.id}', product_id='{self.product_id}', status='{self.status}')>"


class GeneratedAsset(Base):
    """
    Generated media assets from the media studio.
    """
    __tablename__ = 'generated_assets'

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)
    product_id = Column(BigInteger, nullable=False)
    type = Column(String, nullable=False)  # 'image' or 'video'
    file_uri = Column(String, nullable=False)
    preview_uri = Column(String, nullable=True)
    prompt = Column(Text, nullable=True)
    model = Column(String, nullable=True)
    settings = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="generated_assets")

    def __repr__(self):
        return f"<GeneratedAsset(id='{self.id}', product_id='{self.product_id}', type='{self.type}')>"
