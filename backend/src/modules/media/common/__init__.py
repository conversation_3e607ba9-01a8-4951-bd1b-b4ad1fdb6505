"""
Common utilities for media module.
Contains shared functionality to reduce code duplication across providers and engines.
"""

from .language_utils import get_language_instruction, LANGUAGE_INSTRUCTIONS
from .context_creation import create_product_context, create_brand_context, validate_request_context, enrich_request_with_defaults, create_provider_context
from .fallback_handlers import create_fallback_prompt, create_fallback_concept, create_fallback_text_content, handle_generation_fallback
from .config_utils import get_provider_config_value, get_aspect_dimensions, validate_config_structure, get_generation_limits, get_cost_estimates, get_quality_settings, merge_config_with_defaults
from .error_handlers import handle_provider_error, create_error_result, classify_error, should_retry_error, get_retry_delay, create_timeout_error, create_rate_limit_error, log_provider_metrics, validate_provider_response

__all__ = [
    # Language utilities
    "get_language_instruction",
    "LANGUAGE_INSTRUCTIONS",

    # Context creation utilities
    "create_product_context",
    "create_brand_context",
    "validate_request_context",
    "enrich_request_with_defaults",
    "create_provider_context",

    # Fallback handlers
    "create_fallback_prompt",
    "create_fallback_concept",
    "create_fallback_text_content",
    "handle_generation_fallback",

    # Configuration utilities
    "get_provider_config_value",
    "get_aspect_dimensions",
    "validate_config_structure",
    "get_generation_limits",
    "get_cost_estimates",
    "get_quality_settings",
    "merge_config_with_defaults",

    # Error handlers
    "handle_provider_error",
    "create_error_result",
    "classify_error",
    "should_retry_error",
    "get_retry_delay",
    "create_timeout_error",
    "create_rate_limit_error",
    "log_provider_metrics",
    "validate_provider_response"
]