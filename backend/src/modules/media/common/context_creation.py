"""
Common context creation utilities for media providers.
Consolidates duplicate context creation logic across providers.
"""

import logging
from typing import Optional

from ..base import ProviderConfig
from ...schemas import ProviderMediaRequest, ProductContext, ShopBranding, ProductCategory

logger = logging.getLogger(__name__)


def create_product_context(request: ProviderMediaRequest) -> ProductContext:
    """
    Create product context from request data - common across all providers.

    Args:
        request: Provider media request

    Returns:
        ProductContext instance
    """
    from ...context_engine import ProductContext

    # Use category from request's product_context if available, otherwise default
    category = ProductCategory.FASHION_APPAREL  # Default fallback
    if request.product_context and request.product_context.category:
        category = request.product_context.category

    return ProductContext(
        title=request.product_title,
        description=request.product_description or "",
        category=category,
        target_audience=request.target_audience or [],
        key_features=request.product_context.key_features if request.product_context else None,
        materials=request.product_context.materials if request.product_context else None,
        colors=request.product_context.colors if request.product_context else None,
        price_tier=request.product_context.price_tier if request.product_context else None,
        style_keywords=request.product_context.style_keywords if request.product_context else None,
        usage_context=getattr(request, 'usage_context', None) or []
    )


def create_brand_context(request: ProviderMediaRequest) -> Optional['BrandContext']:
    """
    Create brand context from request data - common across all providers.

    Args:
        request: Provider media request

    Returns:
        BrandContext instance or None if no branding provided
    """
    if not request.shop_branding:
        return None

    from ...context_engine import BrandContext

    return BrandContext(
        name=request.shop_branding.shop_name or "Brand",
        brand_voice=request.shop_branding.brand_voice,
        brand_values=request.shop_branding.brand_values or [],
        visual_style=getattr(request, 'visual_style', None) or request.shop_branding.visual_style,
        color_palette=request.shop_branding.color_palette or [],
        industry="e-commerce"  # Default for media providers
    )


def validate_request_context(request: ProviderMediaRequest) -> tuple[bool, Optional[str]]:
    """
    Validate that request has necessary context for generation.

    Args:
        request: Provider media request

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not request.product_title:
        return False, "Product title is required"

    if not request.media_type:
        return False, "Media type is required"

    # Validate media type is supported
    supported_types = ["image", "video", "text", "voice"]
    if request.media_type not in supported_types:
        return False, f"Unsupported media type: {request.media_type}. Supported: {supported_types}"

    # Validate aspect ratio format if provided
    if request.aspect_ratio:
        if not _is_valid_aspect_ratio(request.aspect_ratio):
            return False, f"Invalid aspect ratio format: {request.aspect_ratio}. Expected format: 'width:height'"

    return True, None


def _is_valid_aspect_ratio(aspect_ratio: str) -> bool:
    """
    Validate aspect ratio format.

    Args:
        aspect_ratio: Aspect ratio string (e.g., "16:9", "1:1")

    Returns:
        True if valid format, False otherwise
    """
    if not aspect_ratio:
        return False

    parts = aspect_ratio.split(':')
    if len(parts) != 2:
        return False

    try:
        width, height = int(parts[0]), int(parts[1])
        return width > 0 and height > 0
    except ValueError:
        return False


def enrich_request_with_defaults(request: ProviderMediaRequest) -> ProviderMediaRequest:
    """
    Enrich request with default values where missing.

    Args:
        request: Provider media request

    Returns:
        Enriched request
    """
    # Set default aspect ratio
    if not request.aspect_ratio:
        request.aspect_ratio = "1:1"

    # Set default variants count based on media type
    if not hasattr(request, 'variants_count') or not request.variants_count:
        if request.media_type == "image":
            request.variants_count = 4
        elif request.media_type == "video":
            request.variants_count = 3
        else:
            request.variants_count = 1

    # Set default style
    if not hasattr(request, 'style') or not request.style:
        request.style = "professional"

    return request


def create_provider_context(provider_name: str, config: ProviderConfig) -> dict:
    """
    Create provider-specific context for generation.

    Args:
        provider_name: Name of the provider
        config: Provider configuration

    Returns:
        Dictionary with provider context
    """
    context = {
        'provider_name': provider_name,
        'timeout': config.timeout,
        'max_retries': config.max_retries,
    }

    if hasattr(config, 'metadata') and config.metadata:
        # Add provider-specific metadata
        context.update(config.metadata.get('provider_context', {}))

    return context