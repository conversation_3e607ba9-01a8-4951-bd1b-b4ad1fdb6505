"""
Error handling utilities for media providers.
Standardized error handling and result creation.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def handle_provider_error(
    error: Exception,
    provider_name: str,
    operation: str,
    context: Optional[Dict[str, Any]] = None
) -> 'ProviderMediaResult':
    """
    Handle provider errors with standardized logging and result creation.

    Args:
        error: The exception that occurred
        provider_name: Name of the provider where error occurred
        operation: Description of the operation being performed
        context: Additional context information

    Returns:
        ProviderMediaResult with error information
    """
    error_message = f"{provider_name} {operation} failed: {str(error)}"

    # Log error with context
    log_context = {
        'provider': provider_name,
        'operation': operation,
        'error_type': type(error).__name__,
        'error_message': str(error)
    }

    if context:
        log_context.update(context)

    logger.error(f"Provider error: {error_message}", extra=log_context)

    # Create error result
    return create_error_result(error_message, provider_name, operation)


def create_error_result(
    error_message: str,
    provider_name: str,
    operation: str,
    error_code: Optional[str] = None
) -> 'ProviderMediaResult':
    """
    Create a standardized error result.

    Args:
        error_message: Human-readable error message
        provider_name: Name of the provider
        operation: Operation that failed
        error_code: Optional error code for categorization

    Returns:
        ProviderMediaResult with error details
    """
    from ...schemas import ProviderMediaResult

    return ProviderMediaResult(
        success=False,
        error_message=error_message,
        provider_job_id=f"error_{provider_name}_{operation}",
        estimated_completion_time=0,
        metadata={
            'error_provider': provider_name,
            'error_operation': operation,
            'error_code': error_code or 'GENERIC_ERROR',
            'error_timestamp': 'now'
        }
    )


def classify_error(error: Exception) -> str:
    """
    Classify error type for better handling.

    Args:
        error: The exception to classify

    Returns:
        Error classification string
    """
    error_type = type(error).__name__

    # Network-related errors
    if error_type in ['ConnectionError', 'TimeoutError', 'HTTPError']:
        return 'NETWORK_ERROR'

    # Authentication errors
    elif error_type in ['AuthenticationError', 'UnauthorizedError']:
        return 'AUTHENTICATION_ERROR'

    # Rate limiting
    elif error_type in ['RateLimitError', 'TooManyRequests']:
        return 'RATE_LIMIT_ERROR'

    # Configuration errors
    elif error_type in ['ConfigurationError', 'ValueError']:
        return 'CONFIGURATION_ERROR'

    # Provider-specific errors
    elif 'API' in str(error) or 'provider' in str(error).lower():
        return 'PROVIDER_API_ERROR'

    # Generic errors
    else:
        return 'GENERIC_ERROR'


def should_retry_error(error: Exception, attempt: int, max_attempts: int) -> bool:
    """
    Determine if an error should be retried.

    Args:
        error: The exception that occurred
        attempt: Current attempt number (0-based)
        max_attempts: Maximum number of attempts allowed

    Returns:
        True if should retry, False otherwise
    """
    if attempt >= max_attempts - 1:
        return False

    error_class = classify_error(error)

    # Retry for network and rate limit errors
    retryable_errors = ['NETWORK_ERROR', 'RATE_LIMIT_ERROR', 'PROVIDER_API_ERROR']
    return error_class in retryable_errors


def get_retry_delay(error: Exception, attempt: int) -> float:
    """
    Calculate retry delay based on error type and attempt number.

    Args:
        error: The exception that occurred
        attempt: Current attempt number (0-based)

    Returns:
        Delay in seconds before retrying
    """
    error_class = classify_error(error)

    base_delays = {
        'NETWORK_ERROR': 2.0,
        'RATE_LIMIT_ERROR': 5.0,
        'PROVIDER_API_ERROR': 3.0,
        'GENERIC_ERROR': 1.0
    }

    base_delay = base_delays.get(error_class, 1.0)

    # Exponential backoff
    return base_delay * (2 ** attempt)


def create_timeout_error(provider_name: str, operation: str, timeout_seconds: int) -> 'ProviderMediaResult':
    """
    Create a timeout error result.

    Args:
        provider_name: Name of the provider
        operation: Operation that timed out
        timeout_seconds: Timeout duration in seconds

    Returns:
        ProviderMediaResult for timeout
    """
    error_message = f"{provider_name} {operation} timed out after {timeout_seconds} seconds"
    return create_error_result(error_message, provider_name, operation, 'TIMEOUT_ERROR')


def create_rate_limit_error(provider_name: str, operation: str, retry_after: Optional[int] = None) -> 'ProviderMediaResult':
    """
    Create a rate limit error result.

    Args:
        provider_name: Name of the provider
        operation: Operation that was rate limited
        retry_after: Seconds to wait before retrying

    Returns:
        ProviderMediaResult for rate limiting
    """
    error_message = f"{provider_name} {operation} rate limited"
    if retry_after:
        error_message += f". Retry after {retry_after} seconds"

    return create_error_result(error_message, provider_name, operation, 'RATE_LIMIT_ERROR')


def log_provider_metrics(
    provider_name: str,
    operation: str,
    success: bool,
    duration: float,
    error_type: Optional[str] = None
):
    """
    Log provider performance metrics.

    Args:
        provider_name: Name of the provider
        operation: Operation performed
        success: Whether the operation was successful
        duration: Time taken in seconds
        error_type: Type of error if operation failed
    """
    metrics = {
        'provider': provider_name,
        'operation': operation,
        'success': success,
        'duration_seconds': duration
    }

    if error_type:
        metrics['error_type'] = error_type

    if success:
        logger.info(f"Provider operation completed successfully", extra=metrics)
    else:
        logger.warning(f"Provider operation failed", extra=metrics)


def validate_provider_response(response: Any, expected_type: str) -> tuple[bool, Optional[str]]:
    """
    Validate provider response structure.

    Args:
        response: Response from provider
        expected_type: Expected response type ('image', 'video', 'text')

    Returns:
        Tuple of (is_valid, error_message)
    """
    if response is None:
        return False, "Provider returned null response"

    if expected_type == 'image':
        if not hasattr(response, 'images') or not response.images:
            return False, "Provider response missing images"
    elif expected_type == 'video':
        if not hasattr(response, 'variants') or not response.variants:
            return False, "Provider response missing video variants"
    elif expected_type == 'text':
        if not hasattr(response, 'variants') or not response.variants:
            return False, "Provider response missing text variants"

    return True, None