"""
Configuration utilities for media providers.
Safe configuration access and aspect ratio handling.
"""

import logging
from typing import Any, Optional, Tuple

logger = logging.getLogger(__name__)


def get_provider_config_value(config: 'ProviderConfig', path: str, default=None) -> Any:
    """
    Safe configuration access with dot notation.

    Args:
        config: Provider configuration object
        path: Dot-separated path to config value (e.g., 'image_generation.max_images')
        default: Default value if path not found

    Returns:
        Configuration value or default
    """
    if not hasattr(config, 'metadata') or not config.metadata:
        return default

    keys = path.split('.')
    value = config.metadata

    try:
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    except (KeyError, TypeError):
        return default


def get_aspect_dimensions(aspect_ratio: str) -> Tuple[int, int]:
    """
    Get width and height dimensions for aspect ratio.

    Args:
        aspect_ratio: Aspect ratio string (e.g., "16:9", "1:1")

    Returns:
        Tuple of (width, height)
    """
    default_ratios = {
        "1:1": (1024, 1024),
        "16:9": (1024, 576),
        "9:16": (576, 1024),
        "4:5": (768, 960),
        "3:4": (768, 1024),
        "4:3": (1024, 768),
        "21:9": (1024, 439),
        "9:21": (439, 1024)
    }

    return default_ratios.get(aspect_ratio, (1024, 1024))


def validate_config_structure(config: 'ProviderConfig') -> Tuple[bool, Optional[str]]:
    """
    Validate the structure of provider configuration.

    Args:
        config: Provider configuration to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not config.name:
        return False, "Configuration must have a name"

    if not hasattr(config, 'metadata') or config.metadata is None:
        return False, "Configuration must have metadata"

    # Check for required metadata sections based on supported media types
    if hasattr(config, 'supported_media_types'):
        for media_type in config.supported_media_types:
            expected_section = f"{media_type}_generation"
            if expected_section not in config.metadata:
                logger.warning(f"Missing {expected_section} section in config for {config.name}")

    return True, None


def get_generation_limits(config: 'ProviderConfig', media_type: str) -> dict:
    """
    Get generation limits for a specific media type.

    Args:
        config: Provider configuration
        media_type: Type of media (image, video, text)

    Returns:
        Dictionary with generation limits
    """
    limits = {
        'max_variants': 4,
        'max_resolution': '1024x1024',
        'timeout_seconds': 300,
        'rate_limit_per_minute': 10
    }

    if hasattr(config, 'metadata') and config.metadata:
        media_config = config.metadata.get(f"{media_type}_generation", {})
        limits.update(media_config.get('limits', {}))

    return limits


def get_cost_estimates(config: 'ProviderConfig', media_type: str) -> dict:
    """
    Get cost estimates for a specific media type.

    Args:
        config: Provider configuration
        media_type: Type of media (image, video, text)

    Returns:
        Dictionary with cost estimates
    """
    costs = {
        'per_request': 0.10,
        'per_variant': 0.05,
        'currency': 'USD'
    }

    if hasattr(config, 'metadata') and config.metadata:
        cost_config = config.metadata.get('cost_estimates', {})
        costs.update(cost_config)

    return costs


def get_quality_settings(config: 'ProviderConfig', media_type: str) -> dict:
    """
    Get quality settings for a specific media type.

    Args:
        config: Provider configuration
        media_type: Type of media (image, video, text)

    Returns:
        Dictionary with quality settings
    """
    quality = {
        'min_quality_score': 0.70,
        'preferred_format': 'auto',
        'compression_level': 'balanced'
    }

    if hasattr(config, 'metadata') and config.metadata:
        media_config = config.metadata.get(f"{media_type}_generation", {})
        quality.update(media_config.get('quality', {}))

    return quality


def merge_config_with_defaults(config: 'ProviderConfig', defaults: dict) -> dict:
    """
    Merge provider config with default values.

    Args:
        config: Provider configuration
        defaults: Default configuration values

    Returns:
        Merged configuration dictionary
    """
    merged = defaults.copy()

    if hasattr(config, 'metadata') and config.metadata:
        # Deep merge metadata
        merged.update(config.metadata)

    # Override with explicit config values
    if hasattr(config, 'timeout'):
        merged['timeout'] = config.timeout
    if hasattr(config, 'max_retries'):
        merged['max_retries'] = config.max_retries
    if hasattr(config, 'rate_limit'):
        merged['rate_limit'] = config.rate_limit

    return merged