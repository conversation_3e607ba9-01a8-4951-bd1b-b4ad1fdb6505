"""
Media Module
Provides AI-powered media services for ProductVideo platform.
Generic module that uses provider plugins for different AI services.
"""

# Main services
from .service import media_service

# Engines
from .engines.context_engine import context_engine
from .engines.prompt_engine import prompt_engine
from .engines.quality_engine import quality_engine

# Provider system
from .providers.manager import provider_registry, provider_manager, config

# Models and schemas
from . import models
from . import schemas

__all__ = [
    # Services
    "media_service",

    # Engines
    "context_engine",
    "prompt_engine",
    "quality_engine",

    # Provider system
    "provider_registry",
    "provider_manager",
    "config",

    # Modules
    "models",
    "schemas",
]
