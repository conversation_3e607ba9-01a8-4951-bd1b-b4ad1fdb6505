"""
Media Generation Schemas for E-commerce Media Generation
Comprehensive schemas for professional-grade media generation for e-commerce stores.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, validator, Field
from enum import Enum
from datetime import datetime
import hashlib
import json

from core.schemas.base_schemas import BaseSchema


class ProductCategory(str, Enum):
    """E-commerce product categories for context-aware generation."""
    FASHION_APPAREL = "fashion_apparel"
    FOOTWEAR = "footwear"
    ACCESSORIES = "accessories"
    BEAUTY_COSMETICS = "beauty_cosmetics"
    HOME_DECOR = "home_decor"
    ELECTRONICS = "electronics"
    JEWELRY = "jewelry"
    SPORTS_FITNESS = "sports_fitness"
    LUXURY_GOODS = "luxury_goods"
    CHILDREN_BABY = "children_baby"


class TargetAudience(str, Enum):
    """Target audience segments for personalized content."""
    GEN_Z = "gen_z"  # 18-24
    MILLENNIALS = "millennials"  # 25-40
    GEN_X = "gen_x"  # 41-56
    BABY_BOOMERS = "baby_boomers"  # 57+
    LUXURY_BUYERS = "luxury_buyers"
    BUDGET_CONSCIOUS = "budget_conscious"
    EARLY_ADOPTERS = "early_adopters"
    PROFESSIONALS = "professionals"


class ContentStyle(str, Enum):
    """Content style preferences for brand consistency."""
    MINIMALIST = "minimalist"
    LUXURY = "luxury"
    LIFESTYLE = "lifestyle"
    EDITORIAL = "editorial"
    SOCIAL_VIRAL = "social_viral"
    PROFESSIONAL = "professional"
    ARTISTIC = "artistic"
    COMMERCIAL = "commercial"


class UsageContext(str, Enum):
    """Product usage contexts for lifestyle generation."""
    OFFICE = "office"
    OUTDOOR = "outdoor"
    FITNESS = "fitness"
    CASUAL = "casual"
    FORMAL = "formal"
    TRAVEL = "travel"
    HOME = "home"
    PARTY = "party"
    BEACH = "beach"
    WINTER = "winter"


class GenerationSettings(BaseModel):
    """Detailed generation settings for media creation."""

    # Image/Video settings
    size: Optional[str] = None  # e.g., "1024x1024"
    guidance: Optional[float] = None  # e.g., 7.5
    steps: Optional[int] = None  # e.g., 25
    strength: Optional[float] = None  # e.g., 0.8
    seed: Optional[int] = None  # e.g., 91678

    # Quality and processing
    upscale: Optional[bool] = None  # e.g., True
    safety: Optional[bool] = None  # e.g., True
    quality: Optional[str] = None  # e.g., "Standard"

    # Layout and format
    aspect_ratio: Optional[str] = None  # e.g., "1:1", "16:9"

    # Additional settings
    locale: Optional[str] = "en"  # Language/locale setting

class ShopBranding(BaseModel):
    """Shop branding information for consistent media generation."""
    shop_name: str
    brand_voice: Optional[str] = None  # e.g., "professional", "friendly", "luxury"
    color_palette: Optional[List[str]] = None  # Brand colors
    logo_url: Optional[str] = None
    visual_style: Optional[ContentStyle] = None
    brand_values: Optional[List[str]] = None  # e.g., ["sustainable", "quality", "innovation"]


class ProductContext(BaseModel):
    """Comprehensive product context for professional media generation."""

    # Basic product information
    title: str = Field(..., description="Product title/name")
    description: Optional[str] = Field(None, description="Product description")
    category: Optional[ProductCategory] = Field(None, description="Product category")
    brand: Optional[str] = Field(None, description="Brand name")

    # Physical attributes
    colors: Optional[List[str]] = Field(None, description="Available colors")
    materials: Optional[List[str]] = Field(None, description="Materials used")
    sizes: Optional[List[str]] = Field(None, description="Available sizes")

    # Market positioning
    price: Optional[float] = Field(None, description="Product price")
    currency: Optional[str] = Field("USD", description="Price currency")
    price_tier: Optional[str] = Field(None, description="budget, mid, premium, luxury")

    # Target audience and style
    target_audience: Optional[List[TargetAudience]] = Field(None, description="Target customer segments")
    style_keywords: Optional[List[str]] = Field(None, description="Style descriptors")
    usage_context: Optional[List[UsageContext]] = Field(None, description="Usage scenarios")

    # Features and benefits
    key_features: Optional[List[str]] = Field(None, description="Key product features")
    benefits: Optional[List[str]] = Field(None, description="Customer benefits")
    use_cases: Optional[List[str]] = Field(None, description="Use case scenarios")

    # Shopify metadata
    shopify_product_id: Optional[str] = Field(None, description="Shopify product ID")
    shopify_tags: Optional[List[str]] = Field(None, description="Shopify product tags")
    shopify_collections: Optional[List[str]] = Field(None, description="Shopify collections")
    shopify_vendor: Optional[str] = Field(None, description="Shopify vendor")

    # Existing media
    existing_images: Optional[List[str]] = Field(None, description="Existing product images")
    existing_videos: Optional[List[str]] = Field(None, description="Existing product videos")


class ProductItem(BaseModel):
    """Individual product item for media generation with comprehensive context."""

    product_id: Union[int, str] = Field(..., description="Product identifier")
    prompt: Optional[str] = Field(None, description="Custom generation prompt")
    reference_image_urls: Optional[List[str]] = Field(None, description="Reference images")

    # Comprehensive product context
    product_context: Optional[ProductContext] = Field(None, description="Detailed product information")

    # Shop context
    shop_id: Optional[int] = Field(None, description="Shop identifier")
    shop_branding: Optional[ShopBranding] = Field(None, description="Shop branding information")


class MediaGenerateRequest(BaseSchema):
    """Request to generate media for products with comprehensive e-commerce context."""

    # Core generation parameters
    mode: str = Field(..., description="Generation mode: 'image', 'video', 'text'")
    media_type: Optional[str] = Field(None, description="Specific media type for clarity")
    model: Optional[str] = Field(None, description="Provider model: 'banana', 'veo3', 'gemini', etc.")

    # Detailed generation settings
    settings: Optional[GenerationSettings] = Field(None, description="Generation parameters")

    # Product items with comprehensive context
    items: Optional[List[ProductItem]] = Field(None, description="Products to generate media for")

    # Shop context for brand consistency
    shop_id: Optional[int] = Field(None, description="Shop identifier")
    shop_branding: Optional[ShopBranding] = Field(None, description="Shop branding guidelines")

    # Template and style preferences
    template_id: Optional[str] = Field(None, description="Template identifier")
    content_style: Optional[ContentStyle] = Field(None, description="Content style preference")

    # Generation preferences
    aspect_ratio: Optional[str] = Field("1:1", description="Aspect ratio for media")
    locale: Optional[str] = Field("en", description="Language/locale")

    # Campaign context
    campaign_theme: Optional[str] = Field(None, description="Campaign theme or message")
    call_to_action: Optional[str] = Field(None, description="Specific call to action")

    # Platform targeting
    target_platforms: Optional[List[str]] = Field(None, description="Target platforms: instagram, tiktok, facebook, etc.")

    # Idempotency and versioning
    idempotency_key: Optional[str] = Field(None, description="Idempotency key to prevent duplicate generations")
    product_version: Optional[str] = Field(None, description="Product data version hash")
    force_regenerate: bool = Field(False, description="Force regeneration even if cached version exists")

    # Quality and review controls
    needs_manual_review: bool = Field(False, description="Flag content for manual review")
    qa_metadata: Optional[Dict[str, Any]] = Field(None, description="Quality assurance metadata")

    # Localization
    language: Optional[str] = Field("en", description="Primary language for content generation")
    fallback_language: Optional[str] = Field("en", description="Fallback language if primary fails")

    # Legacy fields for backward compatibility
    product_ids: Optional[List[Union[int, str]]] = Field(None, description="Legacy product IDs")
    voice_id: Optional[str] = Field(None, description="Voice ID for text-to-speech")
    text_input: Optional[str] = Field(None, description="Text input for voice generation")

    def generate_idempotency_key(self) -> str:
        """Generate idempotency key based on product data and template."""
        key_data = {
            "product_ids": self.product_ids or [],
            "items": [item.dict() if item.product_context else {"product_id": item.product_id}
                     for item in (self.items or [])],
            "template_id": self.template_id,
            "mode": self.mode,
            "settings": self.settings.dict() if self.settings else {},
            "content_style": self.content_style.value if self.content_style else None,
            "aspect_ratio": self.aspect_ratio,
            "language": self.language
        }

        # Create hash of key data
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:16]

    def generate_product_version(self, product_data: Dict[str, Any]) -> str:
        """Generate version hash for product data to detect changes."""
        # Include only fields that affect media generation
        version_data = {
            "title": product_data.get("title"),
            "description": product_data.get("description"),
            "category": product_data.get("category"),
            "colors": product_data.get("colors"),
            "materials": product_data.get("materials"),
            "key_features": product_data.get("key_features"),
            "target_audience": product_data.get("target_audience")
        }

        version_string = json.dumps(version_data, sort_keys=True)
        return hashlib.sha256(version_string.encode()).hexdigest()[:12]


class MediaJobInfo(BaseModel):
    """Job information in generate response."""

    product_id: int
    job_id: str  # Changed to str to support external UUID
    status: str
    celery_task_id: Optional[str] = None  # Add Celery task ID


class MediaGenerateResponse(BaseSchema):
    """Response from generate endpoint."""
    
    jobs: List[MediaJobInfo]


class MediaVariantInfo(BaseModel):
    """Media variant information."""
    
    variant_id: int
    variant_name: str
    status: str
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[float] = None


class MediaJobStatusResponse(BaseSchema):
    """Job status response."""
    
    job_id: int
    status: str
    progress: float
    variants: List[MediaVariantInfo]


class PublishOptions(BaseModel):
    """Options for publishing media."""
    alt_text: Optional[str] = None
    position: Optional[int] = None
    replace_existing: bool = False


class MediaPushRequest(BaseSchema):
    """Request to push media to Store."""

    shop_id: int
    product_id: int
    variant_id: int
    publish_targets: List[str] = ["shopify"]  # e.g. shopify, tiktok, youtube
    publish_options: Optional[PublishOptions] = None


class MediaPushResponse(BaseSchema):
    """Response from push endpoint."""
    
    push_id: str
    status: str
    message: str


class ProviderMediaRequest(BaseModel):
    """Internal request schema for media generation providers with comprehensive context."""

    # Basic product information
    product_title: str = Field(..., description="Product title")
    media_type: str = Field(..., description="Type of media to generate")

    # Comprehensive product context
    product_context: Optional[ProductContext] = Field(None, description="Full product context")
    shop_branding: Optional[ShopBranding] = Field(None, description="Shop branding information")

    # Generation parameters
    template_id: Optional[str] = Field(None, description="Template identifier")
    custom_config: Optional[Dict[str, Any]] = Field(None, description="Custom configuration")
    custom_prompt: Optional[str] = Field(None, description="Custom generation prompt")

    # Provider-specific fields
    num_images: Optional[int] = Field(4, description="Number of images to generate")
    variants_count: Optional[int] = Field(4, description="Number of variants to create")
    aspect_ratio: Optional[str] = Field("1:1", description="Aspect ratio")

    # Idempotency and versioning
    idempotency_key: Optional[str] = Field(None, description="Idempotency key for deduplication")
    product_version: Optional[str] = Field(None, description="Product data version")

    # Quality and review
    needs_manual_review: bool = Field(False, description="Requires manual review")
    qa_metadata: Optional[Dict[str, Any]] = Field(None, description="QA metadata")

    # Localization
    language: Optional[str] = Field("en", description="Content language")
    fallback_language: Optional[str] = Field("en", description="Fallback language")
    style: Optional[str] = Field("professional", description="Style preference")
    model: Optional[str] = Field(None, description="Specific model to use")
    settings: Optional[Dict[str, Any]] = Field(None, description="Generation settings")

    # Content targeting
    target_audience: Optional[List[TargetAudience]] = Field(None, description="Target audience")
    usage_context: Optional[List[UsageContext]] = Field(None, description="Usage context")
    content_style: Optional[ContentStyle] = Field(None, description="Content style")

    # Platform and campaign context
    target_platforms: Optional[List[str]] = Field(None, description="Target platforms")
    campaign_theme: Optional[str] = Field(None, description="Campaign theme")
    call_to_action: Optional[str] = Field(None, description="Call to action")

    # Visual style preference
    visual_style: Optional[ContentStyle] = Field(None, description="Visual style preference for media generation")

    # Legacy fields for backward compatibility
    product_description: Optional[str] = Field(None, description="Product description")
    text_input: Optional[str] = Field(None, description="Text input for voice generation")


class ProviderMediaResult(BaseModel):
    """Result from media generation with versioning and QA support."""
    success: bool
    provider_job_id: Optional[str] = None
    images: Optional[List[Dict[str, Any]]] = None
    variants: Optional[List[Dict[str, Any]]] = None
    estimated_completion_time: Optional[int] = None
    error_message: Optional[str] = None

    # Versioning and caching
    idempotency_key: Optional[str] = None
    product_version: Optional[str] = None
    cached_result: bool = False

    # Quality and review
    needs_manual_review: bool = False
    qa_metadata: Optional[Dict[str, Any]] = None
    quality_score: Optional[float] = None

    # Accessibility
    alt_text_generated: bool = False
    captions_generated: bool = False

    # Legal and safety
    brand_safety_checked: bool = False
    copyright_validated: bool = False
    content_flags: Optional[List[str]] = None


class MediaJobListResponse(BaseSchema):
    """Response for listing media jobs."""
    jobs: List[Dict[str, Any]]  # Simplified, could use MediaJobInfo
    total: int
    page: int
    per_page: int


