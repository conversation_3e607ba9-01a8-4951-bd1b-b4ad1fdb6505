"""
Sync-related Celery tasks.

This module contains all tasks related to data synchronization between
Shopify (via Airbyte) and the production database.
"""

import logging
import json
import os
from typing import List, Optional, Dict, Any
from celery import Task

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask

logger = logging.getLogger(__name__)

# Load configuration from config.json
CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config.json')
with open(CONFIG_PATH, 'r') as f:
    CONFIG = json.load(f)

def get_platform_config(platform: str) -> Dict[str, Any]:
    """
    Get platform-specific configuration with defaults fallback.

    Args:
        platform: Platform name (e.g., 'shopify')

    Returns:
        Dict with platform config merged with defaults
    """
    defaults = CONFIG.get('defaults', {})
    platform_config = CONFIG.get('platforms', {}).get(platform, {})

    # Merge platform config with defaults
    config = defaults.copy()
    config.update(platform_config)

    return config


@celery_app.task(name='sync.bulk_sync_store', base=LoggedTask)
def bulk_sync_store(
    store_id: int,
    entity_types: Optional[List[str]] = None,
    triggered_by: str = 'manual',
    webhook_event_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    New simplified bulk sync task that replaces the complex batch system.

    This task uses database-level locking and bulk SQL operations for 100% reliability.

    Args:
        store_id: Store ID to sync (can be int or string that converts to int)
        entity_types: List of entity types to sync (optional)
        triggered_by: What triggered the sync
        webhook_event_id: Optional webhook event ID

    Returns:
        Sync results dictionary
    """
    from core.db.database import SessionLocal
    from modules.sync.processors.bulk_sync_processor import BulkSyncProcessor
    from sqlalchemy import text

    # Handle store_id conversion from string to int if needed
    if isinstance(store_id, str):
        try:
            store_id = int(store_id)
            logger.info(f"Converted store_id from string to int: {store_id}")
        except ValueError:
            logger.error(f"Invalid store_id format: {store_id} (expected integer)")
            return {
                'status': 'failed',
                'error': f'Invalid store_id format: {store_id} (expected integer)',
                'store_id': store_id
            }
    elif not isinstance(store_id, int):
        logger.error(f"Invalid store_id type: {type(store_id)} (expected int or string convertible to int)")
        return {
            'status': 'failed',
            'error': f'Invalid store_id type: {type(store_id)} (expected int or string convertible to int)',
            'store_id': store_id
        }

    logger.info(f"Starting bulk sync for store {store_id}, triggered by {triggered_by}")

    db = SessionLocal()

    lock_id = None
    try:
        # Use PostgreSQL advisory lock to prevent concurrent syncs
        # Convert store_id to hash if it's a string
        lock_id = hash(f"store_{store_id}") % 1000000
        lock_result = db.execute(text("SELECT pg_try_advisory_lock(:lock_id)"), {"lock_id": lock_id})
        lock_acquired = lock_result.scalar()

        if not lock_acquired:
            logger.warning(f"Could not acquire sync lock for store {store_id} - sync already in progress")
            return {
                'status': 'skipped',
                'message': 'Sync already in progress for this store',
                'store_id': store_id
            }

        # Execute bulk sync using the new processor
        processor = BulkSyncProcessor()
        result = processor.sync_all_entities(db, store_id, entity_types)

        logger.info(f"Bulk sync completed for store {store_id}: {result['status']}")
        return result

    except Exception as e:
        logger.error(f"Bulk sync failed for store {store_id}: {e}", exc_info=True)
        # Rollback the transaction to clean up aborted state
        db.rollback()
        raise
    finally:
        # Always release the lock if it was acquired
        if lock_id is not None:
            try:
                db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
            except Exception as unlock_error:
                logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")
        db.close()


@celery_app.task(name='sync.check_sync_status', base=LoggedTask)
def check_sync_status(store_id: int) -> Dict[str, Any]:
    """
    Check if sync is currently running for a store.

    Args:
        store_id: Store ID to check (can be int or string that converts to int)

    Returns:
        Sync status information
    """
    from core.db.database import SessionLocal
    from sqlalchemy import text

    # Handle store_id conversion from string to int if needed
    if isinstance(store_id, str):
        try:
            store_id = int(store_id)
            logger.info(f"Converted store_id from string to int: {store_id}")
        except ValueError:
            logger.error(f"Invalid store_id format: {store_id} (expected integer)")
            return {
                'store_id': store_id,
                'error': f'Invalid store_id format: {store_id} (expected integer)',
                'sync_in_progress': False,
                'lock_available': False
            }
    elif not isinstance(store_id, int):
        logger.error(f"Invalid store_id type: {type(store_id)} (expected int or string convertible to int)")
        return {
            'store_id': store_id,
            'error': f'Invalid store_id type: {type(store_id)} (expected int or string convertible to int)',
            'sync_in_progress': False,
            'lock_available': False
        }

    db = SessionLocal()

    lock_id = None
    try:
        # Check if sync lock is available
        lock_id = hash(f"store_{store_id}") % 1000000
        lock_result = db.execute(text("SELECT pg_try_advisory_lock(:lock_id)"), {"lock_id": lock_id})
        lock_available = lock_result.scalar()

        if lock_available:
            # Release the lock immediately since we were just checking
            try:
                db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
            except Exception as unlock_error:
                logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")

        status = {
            'store_id': store_id,
            'sync_in_progress': not lock_available,
            'lock_available': lock_available,
            'active_job': None  # Could be enhanced to check actual job status
        }

        logger.info(f"Sync status for store {store_id}: {status}")
        return status

    except Exception as e:
        logger.error(f"Error checking sync status for store {store_id}: {e}", exc_info=True)
        # Rollback the transaction to clean up aborted state
        db.rollback()
        raise
    finally:
        # Always release the lock if it was acquired and not already released
        if lock_id is not None:
            try:
                db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
            except Exception as unlock_error:
                logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")
        db.close()


@celery_app.task(name='sync.monitor_scheduled_syncs', base=LoggedTask)
def monitor_scheduled_syncs():
    """
    Monitor for new data in Airbyte tables and trigger syncs as needed.
    
    This task runs periodically to check for new data that needs to be synced
    from Airbyte staging tables to production tables.
    """
    from core.db.database import SessionLocal
    # Import all models to ensure relationships are properly configured
    from core.db import models  # noqa: F401
    from modules.stores.models import Store
    from sqlalchemy import create_engine, text
    from core.config import get_settings
    
    logger.info("Starting scheduled sync monitoring")
    
    db = SessionLocal()
    settings = get_settings()
    
    try:
        # Get all active stores with Airbyte connections
        stores = db.query(Store).filter(
            Store.is_active == True,
            Store.platform == 'shopify'
        ).all()
        
        if not stores:
            logger.info("No active Shopify stores found")
            return
        
        # Create Airbyte database connection
        airbyte_url = (
            f"postgresql://{settings.AIRBYTE_DATABASE_USER}:"
            f"{settings.AIRBYTE_DATABASE_PASSWORD}@"
            f"{settings.AIRBYTE_DATABASE_HOST}:"
            f"{settings.AIRBYTE_DATABASE_PORT}/"
            f"{settings.AIRBYTE_DATABASE_NAME}"
        )
        
        airbyte_engine = create_engine(airbyte_url)
        
        # Check each store for new data
        for store in stores:
            try:
                with airbyte_engine.connect() as airbyte_conn:
                    # Get platform-specific config
                    platform_config = get_platform_config('shopify')
                    entity_types = platform_config['entity_types']

                    for entity_type in entity_types:
                        try:
                            # Check for new data in Airbyte (existing logic)
                            check_interval_minutes = platform_config['sync_check_interval_minutes']
                            new_data_query = f"""
                                SELECT COUNT(*)
                                FROM public.{entity_type}
                                WHERE _airbyte_extracted_at > NOW() - INTERVAL '{check_interval_minutes} minutes'
                            """

                            new_count = airbyte_conn.execute(text(new_data_query)).scalar()

                            # Check for missing data in production (NEW LOGIC)
                            missing_data_detected = False

                            if entity_type == 'inventory_levels':
                                # Check sync_checkpoint and look for NEW data since last sync
                                from modules.sync.models import SyncCheckpoint

                                # Get the last sync time from checkpoint
                                checkpoint = db.query(SyncCheckpoint).filter(
                                    SyncCheckpoint.store_id == store.id,
                                    SyncCheckpoint.entity_type == entity_type
                                ).first()

                                last_sync_time = checkpoint.last_successful_sync_at if checkpoint else None

                                if last_sync_time:
                                    # Check for inventory records NEWER than the last sync time
                                    new_inventory_query = """
                                        SELECT COUNT(*)
                                        FROM public.inventory_levels
                                        WHERE _airbyte_extracted_at > :last_sync_time
                                    """
                                    new_inventory_count = airbyte_conn.execute(text(new_inventory_query), {
                                        'last_sync_time': last_sync_time
                                    }).scalar()

                                    if new_inventory_count > 0:
                                        logger.info(f"New inventory_levels detected: {new_inventory_count} records newer than {last_sync_time}")
                                        missing_data_detected = True
                                else:
                                    # No checkpoint exists - this is the first sync, check if there are ANY inventory records
                                    airbyte_total_query = "SELECT COUNT(*) FROM public.inventory_levels"
                                    airbyte_total = airbyte_conn.execute(text(airbyte_total_query)).scalar()

                                    if airbyte_total > 0:
                                        logger.info(f"First-time inventory sync needed: {airbyte_total} records in Airbyte")
                                        missing_data_detected = True

                            elif entity_type in ['products', 'product_variants', 'product_images']:
                                # Check sync_checkpoint and look for NEW data since last sync
                                from modules.sync.models import SyncCheckpoint

                                # Get the last sync time from checkpoint
                                checkpoint = db.query(SyncCheckpoint).filter(
                                    SyncCheckpoint.store_id == store.id,
                                    SyncCheckpoint.entity_type == entity_type
                                ).first()

                                last_sync_time = checkpoint.last_successful_sync_at if checkpoint else None

                                if last_sync_time:
                                    # Check for records NEWER than the last sync time
                                    new_records_query = f"""
                                        SELECT COUNT(*)
                                        FROM public.{entity_type}
                                        WHERE _airbyte_extracted_at > :last_sync_time
                                    """
                                    new_records_count = airbyte_conn.execute(text(new_records_query), {
                                        'last_sync_time': last_sync_time
                                    }).scalar()

                                    if new_records_count > 0:
                                        logger.info(f"New {entity_type} detected: {new_records_count} records newer than {last_sync_time}")
                                        missing_data_detected = True
                                else:
                                    # No checkpoint exists - this is the first sync, check if there are ANY records
                                    airbyte_total_query = f"SELECT COUNT(*) FROM public.{entity_type}"
                                    airbyte_total = airbyte_conn.execute(text(airbyte_total_query)).scalar()

                                    if airbyte_total > 0:
                                        logger.info(f"First-time sync needed for {entity_type}: {airbyte_total} records in Airbyte")
                                        missing_data_detected = True

                            elif entity_type in ['metafield_products', 'metafield_product_variants', 'metafield_product_images']:
                                # For metafields, check sync_checkpoint and look for NEW data since last sync
                                from modules.sync.models import SyncCheckpoint

                                # Get the last sync time from checkpoint
                                checkpoint = db.query(SyncCheckpoint).filter(
                                    SyncCheckpoint.store_id == store.id,
                                    SyncCheckpoint.entity_type == entity_type
                                ).first()

                                last_sync_time = checkpoint.last_successful_sync_at if checkpoint else None

                                if last_sync_time:
                                    # Check for metafields NEWER than the last sync time
                                    new_metafields_query = f"""
                                        SELECT COUNT(*)
                                        FROM public.{entity_type}
                                        WHERE _airbyte_extracted_at > :last_sync_time
                                    """
                                    new_metafields_count = airbyte_conn.execute(text(new_metafields_query), {
                                        'last_sync_time': last_sync_time
                                    }).scalar()

                                    if new_metafields_count > 0:
                                        logger.info(f"New metafields detected for {entity_type}: {new_metafields_count} records newer than {last_sync_time}")
                                        missing_data_detected = True
                                else:
                                    # No checkpoint exists - this is the first sync, check if there are ANY metafields
                                    airbyte_total_query = f"SELECT COUNT(*) FROM public.{entity_type}"
                                    airbyte_total = airbyte_conn.execute(text(airbyte_total_query)).scalar()

                                    if airbyte_total > 0:
                                        logger.info(f"First-time metafield sync needed for {entity_type}: {airbyte_total} metafields in Airbyte")
                                        missing_data_detected = True

                            # Trigger sync if new data OR missing data detected
                            if new_count > 0 or missing_data_detected:
                                reason = []
                                if new_count > 0:
                                    reason.append(f"{new_count} new records")
                                if missing_data_detected:
                                    reason.append("missing data detected")

                                logger.info(f"Triggering sync for {entity_type} in store {store.id}: {', '.join(reason)}")

                                # Trigger bulk sync for this entity type
                                bulk_sync_store.delay(
                                    store.id,
                                    [entity_type],  # Sync only this entity type
                                    'scheduled'
                                )

                        except Exception as e:
                            logger.warning(f"Error checking {entity_type} for store {store.id}: {e}")
                            continue
                            
            except Exception as e:
                logger.warning(f"Could not connect to Airbyte database for store {store.id}: {e}")
                continue
                
        logger.info("Completed scheduled sync monitoring")
        
    except Exception as e:
        logger.error(f"Error in scheduled sync monitoring: {e}", exc_info=True)
        raise
    finally:
        db.close()


@celery_app.task(name='sync.poll_airbyte_job_status', base=LoggedTask, bind=True)
def poll_airbyte_job_status(self, airbyte_job_id: int, store_id: int, entity_type: str = None, poll_count: int = 0):
    """
    Poll Airbyte job status and update sync statistics when complete.

    Args:
        airbyte_job_id: Airbyte job ID to poll
        store_id: Store ID for the sync
        entity_type: Entity type being synced (optional)
        poll_count: Number of times we've polled this job (for rate limiting)
    """
    from core.db.database import SessionLocal
    from modules.sync.airbyte_service import AirbyteService
    from modules.sync.models import SyncJob, SyncCheckpoint
    from datetime import datetime, timezone

    logger.info(f"Polling Airbyte job {airbyte_job_id} status for store {store_id} (poll #{poll_count})")

    db = SessionLocal()
    airbyte_service = AirbyteService()

    try:
        # Get job details from Airbyte
        job_details = airbyte_service.get_job_details(airbyte_job_id)

        if not job_details:
            logger.warning(f"Could not get job details for Airbyte job {airbyte_job_id}")
            return {'status': 'error', 'message': 'Could not get job details'}

        job_data = job_details.get('job', {})
        job_status = job_data.get('status')

        # Update SyncJob record if it exists
        sync_job = db.query(SyncJob).filter(
            SyncJob.airbyte_job_id == airbyte_job_id
        ).first()

        if sync_job:
            sync_job.status = job_status
            if job_status in ['succeeded', 'failed']:
                sync_job.finished_at = datetime.now(timezone.utc)
                sync_job.duration_seconds = (
                    sync_job.finished_at - sync_job.started_at
                ).total_seconds() if sync_job.started_at else None

                # Get sync stats from Airbyte
                sync_stats = airbyte_service.get_job_sync_stats(airbyte_job_id)
                if sync_stats:
                    sync_job.records_processed = sync_stats.get('total_records_committed', 0)
                    logger.info(f"Updated SyncJob {sync_job.id} with {sync_job.records_processed} records processed")

        # Update SyncCheckpoint if we have sync stats
        if job_status in ['succeeded', 'failed']:
            sync_stats = airbyte_service.get_job_sync_stats(airbyte_job_id)
            if sync_stats:
                # Find the relevant checkpoint
                checkpoint_query = db.query(SyncCheckpoint).filter(
                    SyncCheckpoint.store_id == store_id
                )

                if entity_type:
                    checkpoint_query = checkpoint_query.filter(
                        SyncCheckpoint.entity_type == entity_type
                    )

                checkpoint = checkpoint_query.first()

                if checkpoint:
                    # Update Airbyte-specific fields
                    checkpoint.airbyte_job_id = airbyte_job_id
                    checkpoint.airbyte_sync_finished_at = datetime.now(timezone.utc)

                    # Set Airbyte sync start time if not already set
                    if not checkpoint.airbyte_sync_started_at:
                        checkpoint.airbyte_sync_started_at = checkpoint.sync_started_at

                    # Update record counts from Airbyte stats
                    checkpoint.records_processed_in_sync = sync_stats.get('total_records_committed', 0)

                    # Update Airbyte last sync time
                    checkpoint.airbyte_last_sync_at = datetime.now(timezone.utc)

                    # Log the stats for debugging
                    logger.info(f"Updated SyncCheckpoint for {entity_type} with Airbyte stats: {sync_stats}")
                    logger.info(f"Airbyte job {airbyte_job_id} processed {checkpoint.records_processed_in_sync} records")

                    # Mark sync as completed
                    checkpoint.current_sync_stage = 'completed' if job_status == 'succeeded' else 'failed'
                    checkpoint.last_sync_status = job_status

        db.commit()

        # If job is still running, schedule another poll
        if job_status in ['running', 'pending'] and poll_count < 30:  # Max 30 polls (30 minutes)
            # Schedule next poll in 60 seconds
            poll_airbyte_job_status.apply_async(
                args=[airbyte_job_id, store_id, entity_type, poll_count + 1],
                countdown=60
            )
            logger.info(f"Airbyte job {airbyte_job_id} still running, scheduling next poll in 60 seconds")
        elif job_status in ['succeeded', 'failed']:
            logger.info(f"Airbyte job {airbyte_job_id} completed with status: {job_status}")
        else:
            logger.warning(f"Airbyte job {airbyte_job_id} in unexpected status: {job_status}")

        result = {
            'status': job_status,
            'airbyte_job_id': airbyte_job_id,
            'sync_stats': airbyte_service.get_job_sync_stats(airbyte_job_id),
            'poll_count': poll_count
        }

        logger.info(f"Airbyte job {airbyte_job_id} status: {job_status}")
        return result

    except Exception as e:
        logger.error(f"Error polling Airbyte job {airbyte_job_id}: {e}", exc_info=True)
        db.rollback()
        raise
    finally:
        db.close()


@celery_app.task(name='sync.cleanup_old_sync_jobs', base=LoggedTask)
def cleanup_old_sync_jobs(days_old: Optional[int] = None):
    """
    Clean up old sync job records to prevent database bloat.

    Args:
        days_old: Number of days old records to keep
    """
    from core.db.database import SessionLocal
    from modules.sync.models import SyncJob
    from datetime import datetime, timezone, timedelta

    # Use config value if not provided
    if days_old is None:
        platform_config = get_platform_config('shopify')
        days_old = platform_config['sync_cleanup_days_old']

    db = SessionLocal()

    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

        # Delete old completed sync jobs
        deleted_count = db.query(SyncJob).filter(
            SyncJob.finished_at < cutoff_date,
            SyncJob.status.in_(['completed', 'failed'])
        ).delete()

        db.commit()

        logger.info(f"Cleaned up {deleted_count} old sync job records")
        return {'deleted_count': deleted_count}

    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up old sync jobs: {e}", exc_info=True)
        raise
    finally:
        db.close()


# Task routing configuration is now in config.json under task_routes
