"""add_voice_id_and_script_to_media_jobs

Revision ID: 251b1c081e5d
Revises: 78cda98d6f58
Create Date: 2025-09-13 00:47:42.219980

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '251b1c081e5d'
down_revision: Union[str, Sequence[str], None] = '78cda98d6f58'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_jobs', sa.Column('voice_id', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('script', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('media_jobs', 'script')
    op.drop_column('media_jobs', 'voice_id')
    # ### end Alembic commands ###
