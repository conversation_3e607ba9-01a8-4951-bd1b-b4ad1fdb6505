"""Convert remaining String UUID columns to BigInteger

Revision ID: 78cda98d6f58
Revises: 5b4a17a6d64e
Create Date: 2025-09-13 00:37:43.932411

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '78cda98d6f58'
down_revision: Union[str, Sequence[str], None] = '5b4a17a6d64e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop foreign key constraints first
    op.drop_constraint('generation_requests_batch_id_fkey', 'generation_requests', type_='foreignkey')
    op.drop_constraint('scraping_jobs_document_id_fkey', 'scraping_jobs', type_='foreignkey')
    op.drop_constraint('scraped_products_document_id_fkey', 'scraped_products', type_='foreignkey')
    op.drop_constraint('scraped_collections_document_id_fkey', 'scraped_collections', type_='foreignkey')

    # Alter primary key columns first
    op.alter_column('generated_assets', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('generation_batches', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('generation_requests', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_documents', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_products', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_collections', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraping_jobs', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')

    # Alter foreign key columns
    op.alter_column('generation_requests', 'batch_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='batch_id::bigint')
    op.alter_column('scraped_products', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')
    op.alter_column('scraped_collections', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')
    op.alter_column('scraping_jobs', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')

    # Recreate foreign key constraints
    op.create_foreign_key('generation_requests_batch_id_fkey', 'generation_requests', 'generation_batches', ['batch_id'], ['id'])
    op.create_foreign_key('scraping_jobs_document_id_fkey', 'scraping_jobs', 'scraped_documents', ['document_id'], ['id'])
    op.create_foreign_key('scraped_products_document_id_fkey', 'scraped_products', 'scraped_documents', ['document_id'], ['id'])
    op.create_foreign_key('scraped_collections_document_id_fkey', 'scraped_collections', 'scraped_documents', ['document_id'], ['id'])
    op.alter_column('scraped_collections', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_collections', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')
    op.alter_column('scraped_documents', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_products', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraped_products', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')
    op.alter_column('scraping_jobs', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               postgresql_using='id::bigint')
    op.alter_column('scraping_jobs', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               postgresql_using='document_id::bigint')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('scraping_jobs', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraping_jobs', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraped_products', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraped_products', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraped_documents', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraped_collections', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraped_collections', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generation_requests', 'batch_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('generation_requests', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generation_batches', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generated_assets', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###
